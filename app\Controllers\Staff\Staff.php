<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel as FarmBlockModel;
use App\Models\CropsFarmCropsDataModel as FarmCropsDataModel;
use App\Models\CropsFarmFertilizerDataModel as FarmFertilizerDataModel;
use App\Models\CropsFarmPesticidesDataModel as FarmPesticidesDataModel;
use App\Models\CropsFarmHarvestDataModel as FarmHarvestDataModel;
use App\Models\CropsFarmMarketingDataModel as FarmMarketingDataModel;
use App\Models\provinceModel;
use App\Models\districtModel;
use App\Models\llgModel;
use App\Models\wardModel;
use App\Models\GroupingsModel;
use App\Models\ExerciseModel;

class Staff extends BaseController
{
    protected $session;
    protected $groupingsModel;
    protected $districtModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        
        // Initialize models
        $this->groupingsModel = new GroupingsModel();
        $this->districtModel = new \App\Models\districtModel();
        
        // Verify staff role
        if (session()->get('role') != "user") {
            echo 'Access denied';
            exit;
        }
    }

    public function index()
    {
        // Initialize models for dashboard data
        $farmerModel = new FarmerInformationModel();
        $cropFarmBlockModel = new FarmBlockModel();
        $livestockFarmBlockModel = new \App\Models\LivestockFarmBlockModel();
        $exerciseModel = new \App\Models\ExerciseModel();
        $buyersModel = new \App\Models\CropBuyersModel();

        // Get counts
        $farmer_count = $farmerModel->where('status', 'active')
            ->where('district_id', session()->get('district_id'))
            ->countAllResults();
        
        $crop_blocks_count = $cropFarmBlockModel->where('status', 'active')
            ->where('district_id', session()->get('district_id'))
            ->countAllResults();
        
        $livestock_blocks_count = $livestockFarmBlockModel->where('status', 'active')
            ->where('district_id', session()->get('district_id'))
            ->countAllResults();
        
        $crop_buyers_count = $buyersModel->where('status', 'active')
            ->countAllResults();
        
        // Get active exercises
        $active_exercises = $exerciseModel->where('status', 'active')
            ->where('district_id', session()->get('district_id'))
            ->orderBy('date_to', 'ASC')
            ->findAll(5);
        
        // Get all exercises for the current district
        $exercises = $exerciseModel->select('exercises.*, users.name as officer_name')
            ->join('users', 'users.id = exercises.officer_responsible_id', 'left')
            ->where('exercises.district_id', session()->get('district_id'))
            ->orderBy('exercises.date_from', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Staff Dashboard',
            'page_header' => 'Dashboard',
            'page_desc' => 'Staff Control Panel',
            'page_icon' => 'fa-tachometer-alt',
            'farmer_count' => $farmer_count,
            'crop_blocks_count' => $crop_blocks_count,
            'livestock_blocks_count' => $livestock_blocks_count,
            'crop_buyers_count' => $crop_buyers_count,
            'active_exercises' => $active_exercises,
            'exercises' => $exercises
        ];
        
        return view('staff/staff_dashboard', $data);
    }

    public function workplan($action = 'manage')
    {
        $data = [
            'title' => 'Work Plans',
            'page_header' => 'Work Plans',
            'page_desc' => 'Manage your work plans',
            'page_icon' => 'fa-calendar-alt'
        ];

        if ($action === 'manage') {
            return view('staff/workplan/manage', $data);
        }

        return redirect()->to(base_url('staff/workplan/manage'));
    }

    /**
     * Switch the current district view without changing the default district setting
     */
    public function switch_district($districtId)
    {
        // Get the district details
        $district = $this->districtModel->find($districtId);
        
        if (!$district) {
            return redirect()->back()->with('error', 'District not found');
        }

        // Check if user has permission for this district
        $hasPermission = false;
        $assignedDistricts = session('assigned_districts');
        
        if (!is_array($assignedDistricts)) {
            return redirect()->back()->with('error', 'No districts assigned to your account');
        }

        foreach($assignedDistricts as $assignedDistrict) {
            if ($assignedDistrict['id'] == $districtId) {
                $hasPermission = true;
                break;
            }
        }

        if (!$hasPermission) {
            return redirect()->back()->with('error', 'You do not have permission to access this district');
        }

        // Only update the session view data, not the default district setting
        $this->session->set('district_id', $district['id']);
        $this->session->set('district_name', $district['name']);

        return redirect()->back()->with('success', 'Switched view to ' . $district['name'] . ' district');
    }
}
