<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Data Management</h2>
        <p class="text-muted mb-0">Manage agricultural data including crops, fertilizers, pesticides, and more</p>
    </div>
</div>

<!-- Data Statistics -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-seedling fa-2x mb-2"></i>
                <h4 class="mb-0"><?= count($crops) ?></h4>
                <small>Crops</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-flask fa-2x mb-2"></i>
                <h4 class="mb-0"><?= count($fertilizers) ?></h4>
                <small>Fertilizers</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-spray-can fa-2x mb-2"></i>
                <h4 class="mb-0"><?= count($pesticides) ?></h4>
                <small>Pesticides</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <i class="fas fa-virus fa-2x mb-2"></i>
                <h4 class="mb-0"><?= count($infections) ?></h4>
                <small>Infections</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-horse fa-2x mb-2"></i>
                <h4 class="mb-0"><?= count($livestock) ?></h4>
                <small>Livestock</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                <h4 class="mb-0"><?= count($education) ?></h4>
                <small>Education</small>
            </div>
        </div>
    </div>
</div>

<!-- Data Management Cards -->
<div class="row">
    <!-- Crops Management -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-seedling"></i> Crops Management
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">Manage crop types, varieties, and related information for agricultural data collection.</p>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm">Total Crops</span>
                        <span class="badge bg-primary"><?= count($crops) ?></span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-primary" style="width: 100%"></div>
                    </div>
                </div>

                <?php if (!empty($crops)): ?>
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Recent Crops:</h6>
                        <div class="d-flex flex-wrap gap-1">
                            <?php foreach (array_slice($crops, 0, 5) as $crop): ?>
                                <span class="badge bg-light text-dark border">
                                    <?= esc($crop['crop_name']) ?>
                                </span>
                            <?php endforeach; ?>
                            <?php if (count($crops) > 5): ?>
                                <span class="badge bg-secondary">+<?= count($crops) - 5 ?> more</span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="d-grid">
                    <a href="<?= base_url('dakoii/data/crops') ?>" class="btn btn-primary">
                        <i class="fas fa-cog"></i> Manage Crops
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Fertilizers Management -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-flask"></i> Fertilizers Management
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">Manage fertilizer types, compositions, and application guidelines for crop nutrition.</p>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm">Total Fertilizers</span>
                        <span class="badge bg-success"><?= count($fertilizers) ?></span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-success" style="width: 100%"></div>
                    </div>
                </div>

                <?php if (!empty($fertilizers)): ?>
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Recent Fertilizers:</h6>
                        <div class="d-flex flex-wrap gap-1">
                            <?php foreach (array_slice($fertilizers, 0, 5) as $fertilizer): ?>
                                <span class="badge bg-light text-dark border">
                                    <?= esc($fertilizer['name']) ?>
                                </span>
                            <?php endforeach; ?>
                            <?php if (count($fertilizers) > 5): ?>
                                <span class="badge bg-secondary">+<?= count($fertilizers) - 5 ?> more</span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="d-grid">
                    <a href="<?= base_url('dakoii/data/fertilizers') ?>" class="btn btn-success">
                        <i class="fas fa-cog"></i> Manage Fertilizers
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Pesticides Management -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">
                    <i class="fas fa-spray-can"></i> Pesticides Management
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">Manage pesticide types, active ingredients, and application methods for pest control.</p>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm">Total Pesticides</span>
                        <span class="badge bg-warning text-dark"><?= count($pesticides) ?></span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-warning" style="width: 100%"></div>
                    </div>
                </div>

                <?php if (!empty($pesticides)): ?>
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Recent Pesticides:</h6>
                        <div class="d-flex flex-wrap gap-1">
                            <?php foreach (array_slice($pesticides, 0, 5) as $pesticide): ?>
                                <span class="badge bg-light text-dark border">
                                    <?= esc($pesticide['name']) ?>
                                </span>
                            <?php endforeach; ?>
                            <?php if (count($pesticides) > 5): ?>
                                <span class="badge bg-secondary">+<?= count($pesticides) - 5 ?> more</span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="d-grid">
                    <a href="<?= base_url('dakoii/data/pesticides') ?>" class="btn btn-warning text-dark">
                        <i class="fas fa-cog"></i> Manage Pesticides
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Infections Management -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-virus"></i> Infections Management
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">Manage plant diseases, infections, and treatment protocols for crop health monitoring.</p>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm">Total Infections</span>
                        <span class="badge bg-danger"><?= count($infections) ?></span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-danger" style="width: 100%"></div>
                    </div>
                </div>

                <?php if (!empty($infections)): ?>
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Recent Infections:</h6>
                        <div class="d-flex flex-wrap gap-1">
                            <?php foreach (array_slice($infections, 0, 5) as $infection): ?>
                                <span class="badge bg-light text-dark border">
                                    <?= esc($infection['name']) ?>
                                </span>
                            <?php endforeach; ?>
                            <?php if (count($infections) > 5): ?>
                                <span class="badge bg-secondary">+<?= count($infections) - 5 ?> more</span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="d-grid">
                    <a href="<?= base_url('dakoii/data/infections') ?>" class="btn btn-danger">
                        <i class="fas fa-cog"></i> Manage Infections
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Livestock Management -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-horse"></i> Livestock Management
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">Manage livestock types, breeds, and animal husbandry information for farming data.</p>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm">Total Livestock</span>
                        <span class="badge bg-info"><?= count($livestock) ?></span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-info" style="width: 100%"></div>
                    </div>
                </div>

                <?php if (!empty($livestock)): ?>
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Recent Livestock:</h6>
                        <div class="d-flex flex-wrap gap-1">
                            <?php foreach (array_slice($livestock, 0, 5) as $animal): ?>
                                <span class="badge bg-light text-dark border">
                                    <?= esc($animal['livestock_name']) ?>
                                </span>
                            <?php endforeach; ?>
                            <?php if (count($livestock) > 5): ?>
                                <span class="badge bg-secondary">+<?= count($livestock) - 5 ?> more</span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="d-grid">
                    <a href="<?= base_url('dakoii/data/livestock') ?>" class="btn btn-info">
                        <i class="fas fa-cog"></i> Manage Livestock
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Education Management -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-graduation-cap"></i> Education Management
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">Manage educational resources, training materials, and knowledge base for farmers.</p>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm">Total Education Items</span>
                        <span class="badge bg-secondary"><?= count($education) ?></span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-secondary" style="width: 100%"></div>
                    </div>
                </div>

                <?php if (!empty($education)): ?>
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Recent Education:</h6>
                        <div class="d-flex flex-wrap gap-1">
                            <?php foreach (array_slice($education, 0, 5) as $edu): ?>
                                <span class="badge bg-light text-dark border">
                                    <?= esc($edu['name'] ?? $edu['title'] ?? 'Education Item') ?>
                                </span>
                            <?php endforeach; ?>
                            <?php if (count($education) > 5): ?>
                                <span class="badge bg-secondary">+<?= count($education) - 5 ?> more</span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="d-grid">
                    <a href="<?= base_url('dakoii/data/education') ?>" class="btn btn-secondary">
                        <i class="fas fa-cog"></i> Manage Education
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.text-sm {
    font-size: 0.875rem;
}

.progress {
    background-color: #f8f9fc;
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}
</style>

<?= $this->endSection() ?>
