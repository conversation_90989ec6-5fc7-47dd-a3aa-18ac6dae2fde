<?php

namespace App\Models;

use CodeIgniter\Model;

class LivestockModel extends Model
{
    protected $table            = 'adx_livestock';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'livestock_name',
        'livestock_icon',
        'livestock_color_code',
        'remarks',
        'created_by',
        'created_at',
        'updated_by',
        'updated_at',
        'status'
    ];

    // Timestamps
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Skip validation
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Validation Rules
    protected $validationRules = [
        'livestock_name' => 'required|min_length[2]|max_length[100]',
        'livestock_color_code' => 'permit_empty|max_length[7]',
        'status' => 'required|in_list[active,inactive,deleted]'
    ];

    protected $validationMessages = [
        'livestock_name' => [
            'required' => 'Livestock name is required',
            'min_length' => 'Livestock name must be at least 2 characters long',
            'max_length' => 'Livestock name cannot exceed 100 characters'
        ],
        'livestock_color_code' => [
            'max_length' => 'Color code cannot exceed 7 characters'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Invalid status value'
        ]
    ];

    // Helper methods
    public function getLivestock()
    {
        return $this->where('status', 'active')
                    ->orderBy('livestock_name', 'ASC')
                    ->findAll();
    }

    public function getLivestockById($id)
    {
        return $this->find($id);
    }

    public function getLivestockName($id)
    {
        $livestock = $this->getLivestockById($id);
        return $livestock ? $livestock['livestock_name'] : null;
    }

    public function getLivestockIcon($id)
    {
        $livestock = $this->getLivestockById($id);
        return $livestock ? $livestock['livestock_icon'] : null;
    }
    
    public function getLivestockColorCode($id)
    {
        $livestock = $this->getLivestockById($id);
        return $livestock ? $livestock['livestock_color_code'] : null;
    }
    

    public function getLivestockInfo($identifier)
    {
        return $this->find($identifier);
    }

    public function getIdFromValue($value)
    {
        return $value;  // In new system, value is the ID
    }

    // Get active livestock
    public function getActiveLivestock()
    {
        return $this->where('status', 'active')
                    ->orderBy('livestock_name', 'ASC')
                    ->findAll();
    }

    // Get livestock with details
    public function getLivestockWithDetails()
    {
        return $this->select('adx_livestock.*, users.name as created_by_name')
                    ->join('users', 'users.id = adx_livestock.created_by', 'left')
                    ->orderBy('adx_livestock.livestock_name', 'ASC')
                    ->findAll();
    }

    // Get active livestock with details
    public function getActiveLivestockWithDetails()
    {
        return $this->select('
                adx_livestock.*,
                COALESCE(
                    (SELECT SUM(he_total + she_total) 
                    FROM livestock_farm_data 
                    WHERE livestock_farm_data.livestock_id = adx_livestock.id 
                    AND livestock_farm_data.status = "active"), 0
                ) as total_count,
                COALESCE(
                    (SELECT COUNT(DISTINCT block_id) 
                    FROM livestock_farm_data 
                    WHERE livestock_farm_data.livestock_id = adx_livestock.id 
                    AND livestock_farm_data.status = "active"), 0
                ) as total_blocks
            ')
            ->where('adx_livestock.status', 'active')
            ->orderBy('adx_livestock.livestock_name', 'ASC')
            ->findAll();
    }
}