<?= $this->extend("templates/dakoiitemp"); ?>
<?= $this->section('content'); ?>

<body>
    <div class="container-fluid p-2">
        <div class="row d-flex justify-content-center p-lg-5">
            <div class=" col-md-4 ">

                <div class="card shadow-lg rounded-top-5 ">
                    <div class="card-header text-center">
                        Dakoii Admin Login
                    </div>
                    <div class="card-body">
                        <?php if (session()->has('error')) : ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle"></i> <?= session('error') ?>
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        <?php endif; ?>

                        <?php if (session()->has('success')) : ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle"></i> <?= session('success') ?>
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        <?php endif; ?>

                        <?= form_open('dakoii/login', ['id' => 'loginForm']) ?>
                        <div class="form-group">
                            <label for="username" class="sr-only">Username</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                </div>
                                <input type="text" class="form-control" id="username" name="username"
                                       placeholder="Enter your username" required maxlength="255"
                                       value="<?= old('username') ?>">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="password" class="sr-only">Password</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                </div>
                                <input type="password" class="form-control" id="password" name="password"
                                       placeholder="Enter your password" required>
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                        <i class="fas fa-eye" id="toggleIcon"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg btn-block" id="loginBtn">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </button>
                        <?= form_close() ?>
                    </div>
                    <div class="card-footer text-center">
                        <small>Dakoii Administrators Login</small>
                    </div>
                </div>

            </div>

        </div>
    </div>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>

    <script>
        $(document).ready(function() {
            // Password toggle functionality
            $('#togglePassword').click(function() {
                const passwordField = $('#password');
                const toggleIcon = $('#toggleIcon');

                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    toggleIcon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    toggleIcon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });

            // Form submission with loading state
            $('#loginForm').submit(function() {
                const loginBtn = $('#loginBtn');
                loginBtn.prop('disabled', true);
                loginBtn.html('<i class="fas fa-spinner fa-spin"></i> Logging in...');

                // Re-enable button after 5 seconds as fallback
                setTimeout(function() {
                    loginBtn.prop('disabled', false);
                    loginBtn.html('<i class="fas fa-sign-in-alt"></i> Login');
                }, 5000);
            });

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);

            // Focus on username field when page loads
            $('#username').focus();
        });
    </script>
</body>



</html>
<?= $this->endSection() ?>