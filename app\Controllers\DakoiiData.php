<?php

namespace App\Controllers;

class DakoiiData extends BaseController
{
    public $session;
    public $cropsModel;
    public $fertilizersModel;
    public $pesticidesModel;
    public $infectionsModel;
    public $livestockModel;
    public $educationModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        
        // Initialize models
        $this->cropsModel = new \App\Models\CropsModel();
        $this->fertilizersModel = new \App\Models\FertilizersModel();
        $this->pesticidesModel = new \App\Models\PesticidesModel();
        $this->infectionsModel = new \App\Models\InfectionsModel();
        $this->livestockModel = new \App\Models\LivestockModel();
        $this->educationModel = new \App\Models\EducationModel();
    }

    /**
     * Display data management dashboard
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Data Management";
        $data['menu'] = "data";
        
        // Get all data for overview
        $data['crops'] = $this->cropsModel->findAll();
        $data['fertilizers'] = $this->fertilizersModel->findAll();
        $data['pesticides'] = $this->pesticidesModel->findAll();
        $data['infections'] = $this->infectionsModel->findAll();
        $data['livestock'] = $this->livestockModel->findAll();
        $data['education'] = $this->educationModel->findAll();

        return view('dakoii/dakoii_data_index', $data);
    }

    /**
     * Display crops management
     */
    public function crops()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Crops Management";
        $data['menu'] = "data";
        $data['crops'] = $this->cropsModel->findAll();

        return view('dakoii/dakoii_data_crops', $data);
    }

    /**
     * Store new crop
     */
    public function storeCrop()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/crops');
        }

        // Handle file upload
        $icon = $this->request->getFile('crop_icon');
        $iconPath = '';
        
        if ($icon && $icon->isValid() && !$icon->hasMoved()) {
            $newName = $icon->getRandomName();
            
            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/icons/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            
            $icon->move($uploadPath, $newName);
            $iconPath = 'public/uploads/icons/' . $newName;
        }

        $data = [
            'crop_name' => $this->request->getPost('crop_name'),
            'crop_icon' => $iconPath,
            'crop_color_code' => $this->request->getPost('crop_color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->cropsModel->insert($data)) {
            session()->setFlashdata('success', 'Crop added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add crop');
        }
        
        return redirect()->to('dakoii/data/crops');
    }

    /**
     * Update crop
     */
    public function updateCrop($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/crops');
        }

        // Handle file upload
        $icon = $this->request->getFile('crop_icon');
        $data = [
            'crop_name' => $this->request->getPost('crop_name'),
            'crop_color_code' => $this->request->getPost('crop_color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($icon && $icon->isValid() && !$icon->hasMoved()) {
            // Delete old icon if exists
            $oldCrop = $this->cropsModel->find($id);
            if (!empty($oldCrop['crop_icon'])) {
                $oldIconPath = ROOTPATH . $oldCrop['crop_icon'];
                if (file_exists($oldIconPath)) {
                    unlink($oldIconPath);
                }
            }

            // Save new icon
            $newName = $icon->getRandomName();
            
            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/icons/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            
            $icon->move($uploadPath, $newName);
            $data['crop_icon'] = 'public/uploads/icons/' . $newName;
        }

        if ($this->cropsModel->update($id, $data)) {
            session()->setFlashdata('success', 'Crop updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update crop');
        }
        
        return redirect()->to('dakoii/data/crops');
    }

    /**
     * Display fertilizers management
     */
    public function fertilizers()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Fertilizers Management";
        $data['menu'] = "data";
        $data['fertilizers'] = $this->fertilizersModel->findAll();

        return view('dakoii/dakoii_data_fertilizers', $data);
    }

    /**
     * Store new fertilizer
     */
    public function storeFertilizer()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/fertilizers');
        }

        // Handle file upload
        $icon = $this->request->getFile('icon');
        $iconPath = '';
        
        if ($icon && $icon->isValid() && !$icon->hasMoved()) {
            $newName = $icon->getRandomName();
            
            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/icons/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            
            $icon->move($uploadPath, $newName);
            $iconPath = 'public/uploads/icons/' . $newName;
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'icon' => $iconPath,
            'color_code' => $this->request->getPost('color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->fertilizersModel->insert($data)) {
            session()->setFlashdata('success', 'Fertilizer added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add fertilizer');
        }
        
        return redirect()->to('dakoii/data/fertilizers');
    }

    /**
     * Update fertilizer
     */
    public function updateFertilizer($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/fertilizers');
        }

        // Handle file upload
        $icon = $this->request->getFile('icon');
        $data = [
            'name' => $this->request->getPost('name'),
            'color_code' => $this->request->getPost('color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($icon && $icon->isValid() && !$icon->hasMoved()) {
            // Delete old icon if exists
            $oldFertilizer = $this->fertilizersModel->find($id);
            if (!empty($oldFertilizer['icon'])) {
                $oldIconPath = ROOTPATH . $oldFertilizer['icon'];
                if (file_exists($oldIconPath)) {
                    unlink($oldIconPath);
                }
            }

            // Save new icon
            $newName = $icon->getRandomName();
            
            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/icons/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            
            $icon->move($uploadPath, $newName);
            $data['icon'] = 'public/uploads/icons/' . $newName;
        }

        if ($this->fertilizersModel->update($id, $data)) {
            session()->setFlashdata('success', 'Fertilizer updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update fertilizer');
        }
        
        return redirect()->to('dakoii/data/fertilizers');
    }

    /**
     * Display pesticides management
     */
    public function pesticides()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Pesticides Management";
        $data['menu'] = "data";
        $data['pesticides'] = $this->pesticidesModel->findAll();

        return view('dakoii/dakoii_data_pesticides', $data);
    }

    /**
     * Store new pesticide
     */
    public function storePesticide()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/pesticides');
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'icon' => $this->request->getPost('icon'),
            'color_code' => $this->request->getPost('color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->pesticidesModel->insert($data)) {
            session()->setFlashdata('success', 'Pesticide added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add pesticide');
        }

        return redirect()->to('dakoii/data/pesticides');
    }

    /**
     * Update pesticide
     */
    public function updatePesticide($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/pesticides');
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'icon' => $this->request->getPost('icon'),
            'color_code' => $this->request->getPost('color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->pesticidesModel->update($id, $data)) {
            session()->setFlashdata('success', 'Pesticide updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update pesticide');
        }

        return redirect()->to('dakoii/data/pesticides');
    }

    /**
     * Display infections management
     */
    public function infections()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Infections Management";
        $data['menu'] = "data";
        $data['infections'] = $this->infectionsModel->findAll();

        return view('dakoii/dakoii_data_infections', $data);
    }

    /**
     * Store new infection
     */
    public function storeInfection()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/infections');
        }

        // Handle file upload
        $icon = $this->request->getFile('icon');
        $iconPath = '';

        if ($icon && $icon->isValid() && !$icon->hasMoved()) {
            $newName = $icon->getRandomName();

            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/icons/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $icon->move($uploadPath, $newName);
            $iconPath = 'public/uploads/icons/' . $newName;
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'icon' => $iconPath,
            'color_code' => $this->request->getPost('color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->infectionsModel->insert($data)) {
            session()->setFlashdata('success', 'Infection added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add infection');
        }

        return redirect()->to('dakoii/data/infections');
    }

    /**
     * Update infection
     */
    public function updateInfection($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/infections');
        }

        // Handle file upload
        $icon = $this->request->getFile('icon');
        $data = [
            'name' => $this->request->getPost('name'),
            'color_code' => $this->request->getPost('color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($icon && $icon->isValid() && !$icon->hasMoved()) {
            // Delete old icon if exists
            $oldInfection = $this->infectionsModel->find($id);
            if (!empty($oldInfection['icon'])) {
                $oldIconPath = ROOTPATH . $oldInfection['icon'];
                if (file_exists($oldIconPath)) {
                    unlink($oldIconPath);
                }
            }

            // Save new icon
            $newName = $icon->getRandomName();

            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/icons/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $icon->move($uploadPath, $newName);
            $data['icon'] = 'public/uploads/icons/' . $newName;
        }

        if ($this->infectionsModel->update($id, $data)) {
            session()->setFlashdata('success', 'Infection updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update infection');
        }

        return redirect()->to('dakoii/data/infections');
    }

    /**
     * Display livestock management
     */
    public function livestock()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Livestock Management";
        $data['menu'] = "data";
        $data['livestock'] = $this->livestockModel->findAll();

        return view('dakoii/dakoii_data_livestock', $data);
    }

    /**
     * Store new livestock
     */
    public function storeLivestock()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/livestock');
        }

        // Handle file upload
        $icon = $this->request->getFile('livestock_icon');
        $iconPath = '';

        if ($icon && $icon->isValid() && !$icon->hasMoved()) {
            $newName = $icon->getRandomName();

            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/icons/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $icon->move($uploadPath, $newName);
            $iconPath = 'public/uploads/icons/' . $newName;
        }

        $data = [
            'livestock_name' => $this->request->getPost('livestock_name'),
            'livestock_icon' => $iconPath,
            'livestock_color_code' => $this->request->getPost('livestock_color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->livestockModel->insert($data)) {
            session()->setFlashdata('success', 'Livestock added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add livestock');
        }

        return redirect()->to('dakoii/data/livestock');
    }

    /**
     * Update livestock
     */
    public function updateLivestock($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/livestock');
        }

        // Handle file upload
        $icon = $this->request->getFile('livestock_icon');
        $data = [
            'livestock_name' => $this->request->getPost('livestock_name'),
            'livestock_color_code' => $this->request->getPost('livestock_color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($icon && $icon->isValid() && !$icon->hasMoved()) {
            // Delete old icon if exists
            $oldLivestock = $this->livestockModel->find($id);
            if (!empty($oldLivestock['livestock_icon'])) {
                $oldIconPath = ROOTPATH . $oldLivestock['livestock_icon'];
                if (file_exists($oldIconPath)) {
                    unlink($oldIconPath);
                }
            }

            // Save new icon
            $newName = $icon->getRandomName();

            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/icons/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $icon->move($uploadPath, $newName);
            $data['livestock_icon'] = 'public/uploads/icons/' . $newName;
        }

        if ($this->livestockModel->update($id, $data)) {
            session()->setFlashdata('success', 'Livestock updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update livestock');
        }

        return redirect()->to('dakoii/data/livestock');
    }

    /**
     * Display education management
     */
    public function education()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Education Management";
        $data['menu'] = "data";
        $data['education'] = $this->educationModel->findAll();

        return view('dakoii/dakoii_data_education', $data);
    }

    /**
     * Store new education item
     */
    public function storeEducation()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/education');
        }

        // Handle file upload
        $icon = $this->request->getFile('icon');
        $iconPath = '';

        if ($icon && $icon->isValid() && !$icon->hasMoved()) {
            $newName = $icon->getRandomName();

            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/icons/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $icon->move($uploadPath, $newName);
            $iconPath = 'public/uploads/icons/' . $newName;
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'icon' => $iconPath,
            'color_code' => $this->request->getPost('color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->educationModel->insert($data)) {
            session()->setFlashdata('success', 'Education item added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add education item');
        }

        return redirect()->to('dakoii/data/education');
    }

    /**
     * Update education item
     */
    public function updateEducation($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/data/education');
        }

        // Handle file upload
        $icon = $this->request->getFile('icon');
        $data = [
            'name' => $this->request->getPost('name'),
            'color_code' => $this->request->getPost('color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($icon && $icon->isValid() && !$icon->hasMoved()) {
            // Delete old icon if exists
            $oldEducation = $this->educationModel->find($id);
            if (!empty($oldEducation['icon'])) {
                $oldIconPath = ROOTPATH . $oldEducation['icon'];
                if (file_exists($oldIconPath)) {
                    unlink($oldIconPath);
                }
            }

            // Save new icon
            $newName = $icon->getRandomName();

            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/icons/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $icon->move($uploadPath, $newName);
            $data['icon'] = 'public/uploads/icons/' . $newName;
        }

        if ($this->educationModel->update($id, $data)) {
            session()->setFlashdata('success', 'Education item updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update education item');
        }

        return redirect()->to('dakoii/data/education');
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }
}
