<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/data') ?>">Data Management</a></li>
        <li class="breadcrumb-item active">Livestock</li>
    </ol>
</nav>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Livestock Management</h2>
        <p class="text-muted mb-0">Manage livestock types and animal husbandry information for farming data</p>
    </div>
    <div class="btn-group">
        <a href="<?= base_url('dakoii/data') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Data
        </a>
        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#addLivestockModal">
            <i class="fas fa-plus"></i> Add Livestock
        </button>
    </div>
</div>

<!-- Livestock Statistics -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Total Livestock</h6>
                        <h3 class="mb-0"><?= count($livestock) ?></h3>
                        <small>Livestock types available in the system</small>
                    </div>
                    <i class="fas fa-horse fa-3x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Livestock Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list"></i> Livestock List
            </h5>
            <input type="text" class="form-control form-control-sm" id="searchInput" 
                   placeholder="Search livestock..." style="width: 250px;">
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="livestockTable">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>Icon</th>
                        <th>Livestock Name</th>
                        <th>Color Code</th>
                        <th>Remarks</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($livestock)): ?>
                        <?php $i = 1; foreach ($livestock as $animal): ?>
                        <tr>
                            <td><?= $i++ ?></td>
                            <td>
                                <?php if (!empty($animal['livestock_icon'])): ?>
                                    <img src="<?= base_url($animal['livestock_icon']) ?>" alt="Livestock Icon" 
                                         class="rounded" style="width: 30px; height: 30px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-secondary rounded d-flex align-items-center justify-content-center" 
                                         style="width: 30px; height: 30px;">
                                        <i class="fas fa-horse text-white"></i>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="fw-medium"><?= esc($animal['livestock_name']) ?></div>
                                <small class="text-muted">ID: <?= $animal['id'] ?></small>
                            </td>
                            <td>
                                <?php if (!empty($animal['livestock_color_code'])): ?>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded me-2" 
                                             style="width: 20px; height: 20px; background-color: <?= esc($animal['livestock_color_code']) ?>; border: 1px solid #dee2e6;"></div>
                                        <code><?= esc($animal['livestock_color_code']) ?></code>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">No color</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if (!empty($animal['remarks'])): ?>
                                    <span title="<?= esc($animal['remarks']) ?>">
                                        <?= strlen($animal['remarks']) > 50 ? esc(substr($animal['remarks'], 0, 50)) . '...' : esc($animal['remarks']) ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">No remarks</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary edit-livestock" 
                                            data-id="<?= $animal['id'] ?>"
                                            data-name="<?= esc($animal['livestock_name']) ?>"
                                            data-color="<?= esc($animal['livestock_color_code']) ?>"
                                            data-remarks="<?= esc($animal['remarks']) ?>"
                                            data-bs-toggle="modal" 
                                            data-bs-target="#editLivestockModal"
                                            title="Edit Livestock">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <i class="fas fa-horse fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No livestock found</p>
                                <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#addLivestockModal">
                                    <i class="fas fa-plus"></i> Add First Livestock
                                </button>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <small class="text-muted">
                Showing <span id="showingCount"><?= count($livestock) ?></span> of <?= count($livestock) ?> livestock
            </small>
        </div>
    </div>
</div>

<!-- Add Livestock Modal -->
<div class="modal fade" id="addLivestockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> Add New Livestock
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open_multipart('dakoii/data/livestock/store') ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="livestock_name" class="form-label">Livestock Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="livestock_name" name="livestock_name" required>
                </div>

                <div class="mb-3">
                    <label for="livestock_icon" class="form-label">Livestock Icon</label>
                    <input type="file" class="form-control" id="livestock_icon" name="livestock_icon" accept="image/*">
                    <div class="form-text">Upload an icon for this livestock (optional)</div>
                </div>

                <div class="mb-3">
                    <label for="livestock_color_code" class="form-label">Color Code</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="livestock_color_code" name="livestock_color_code" value="#0dcaf0">
                        <input type="text" class="form-control" id="color_text" placeholder="#0dcaf0" readonly>
                    </div>
                    <div class="form-text">Choose a color to represent this livestock</div>
                </div>

                <div class="mb-3">
                    <label for="remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Additional notes about this livestock..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-info">
                    <i class="fas fa-save"></i> Add Livestock
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Livestock Modal -->
<div class="modal fade" id="editLivestockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> Edit Livestock
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editLivestockForm" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_livestock_name" class="form-label">Livestock Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_livestock_name" name="livestock_name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_livestock_icon" class="form-label">Livestock Icon</label>
                        <input type="file" class="form-control" id="edit_livestock_icon" name="livestock_icon" accept="image/*">
                        <div class="form-text">Upload a new icon to replace the current one (optional)</div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_livestock_color_code" class="form-label">Color Code</label>
                        <div class="input-group">
                            <input type="color" class="form-control form-control-color" id="edit_livestock_color_code" name="livestock_color_code">
                            <input type="text" class="form-control" id="edit_color_text" readonly>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="edit_remarks" name="remarks" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-save"></i> Update Livestock
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.opacity-75 {
    opacity: 0.75;
}

.table td {
    vertical-align: middle;
}

.form-control-color {
    width: 3rem;
    height: calc(2.25rem + 2px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const table = document.getElementById('livestockTable');
    const tbody = table.querySelector('tbody');
    const showingCount = document.getElementById('showingCount');

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = tbody.querySelectorAll('tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        showingCount.textContent = visibleCount;
    });

    // Color picker sync for add modal
    const colorPicker = document.getElementById('livestock_color_code');
    const colorText = document.getElementById('color_text');
    
    colorPicker.addEventListener('input', function() {
        colorText.value = this.value;
    });

    // Color picker sync for edit modal
    const editColorPicker = document.getElementById('edit_livestock_color_code');
    const editColorText = document.getElementById('edit_color_text');
    
    editColorPicker.addEventListener('input', function() {
        editColorText.value = this.value;
    });

    // Handle edit livestock button clicks
    document.querySelectorAll('.edit-livestock').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.dataset.id;
            const name = this.dataset.name;
            const color = this.dataset.color;
            const remarks = this.dataset.remarks;
            
            document.getElementById('edit_livestock_name').value = name;
            document.getElementById('edit_livestock_color_code').value = color || '#0dcaf0';
            document.getElementById('edit_color_text').value = color || '#0dcaf0';
            document.getElementById('edit_remarks').value = remarks;
            
            document.getElementById('editLivestockForm').action = '<?= base_url('dakoii/data/livestock/update/') ?>' + id;
        });
    });

    // Initialize color text on page load
    colorText.value = colorPicker.value;
});
</script>

<?= $this->endSection() ?>
