-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: May 02, 2025 at 02:23 AM
-- Server version: 10.5.25-MariaDB-cll-lve
-- PHP Version: 8.1.32

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `dakoiim1_agristats_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `adx_country`
--

CREATE TABLE `adx_country` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(2) NOT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_country`
--

INSERT INTO `adx_country` (`id`, `name`, `code`, `created_at`) VALUES
(1, 'Papua New Guinea', 'PG', '2023-03-11 10:10:42'),
(2, 'Australia', 'AU', '2023-03-11 10:10:42');

-- --------------------------------------------------------

--
-- Table structure for table `adx_crops`
--

CREATE TABLE `adx_crops` (
  `id` int(11) NOT NULL,
  `crop_name` varchar(255) NOT NULL,
  `crop_icon` varchar(255) NOT NULL,
  `crop_color_code` varchar(7) NOT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_by` int(11) NOT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_crops`
--

INSERT INTO `adx_crops` (`id`, `crop_name`, `crop_icon`, `crop_color_code`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(1, 'Cocoa', 'public/uploads/icons/1732157338_332687c4e9f9e3463a6d.png', '#8a630f', 'This si cocoa', 0, NULL, 0, '2024-11-21 12:48:58'),
(4, 'Coffee', 'public/uploads/icons/1732157982_1a69de9622b3904124dd.png', '#f72b2b', 'Cofffee', 2, '2024-11-21 12:59:42', 2, '2024-11-21 13:01:30'),
(5, 'Rubber', 'public/uploads/icons/1732740553_ab42770fe27bb159e301.png', '#331414', 'rubber crop', 2, '2024-11-28 06:49:13', 0, '2024-11-28 06:49:13');

-- --------------------------------------------------------

--
-- Table structure for table `adx_district`
--

CREATE TABLE `adx_district` (
  `id` int(11) NOT NULL,
  `districtcode` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `json_id` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_district`
--

INSERT INTO `adx_district` (`id`, `districtcode`, `name`, `country_id`, `province_id`, `json_id`) VALUES
(1, '', 'Abau', 1, 86, ''),
(2, '', 'Goilala', 1, 86, ''),
(3, '', 'Kairuku-Hiri', 1, 86, ''),
(4, '', 'Rigo', 1, 86, ''),
(5, '', 'Chimbu', 1, 87, ''),
(6, '', 'Gumine', 1, 87, ''),
(7, '', 'Kerowagi', 1, 87, ''),
(8, '', 'Kundiawa-Gembogl', 1, 87, ''),
(9, '', 'Sinasina-Yonggomugl', 1, 87, ''),
(10, '', 'Goroka', 1, 88, ''),
(11, '', 'Kainantu', 1, 88, ''),
(12, '', 'Lufa', 1, 88, ''),
(13, '', 'Obura-Wonenara', 1, 88, ''),
(14, '', 'Okapa', 1, 88, ''),
(15, '', 'Gazelle', 1, 89, ''),
(16, '', 'Kokopo', 1, 89, ''),
(17, '', 'Pomio', 1, 89, ''),
(18, '', 'Rabaul', 1, 89, ''),
(19, '1401', 'Ambunti-Dreikikir District', 1, 90, 'OCNPNG00301401'),
(20, '1402', 'Angoram District', 1, 90, 'OCNPNG00301402'),
(21, '1403', 'Maprik District', 1, 90, ''),
(22, '1404', 'Wewak District', 1, 90, 'OCNPNG00301404'),
(24, '', 'Kompiam-Ambum', 1, 91, ''),
(25, '', 'Laiagam-Porgera', 1, 91, ''),
(26, '', 'Wabag', 1, 91, ''),
(27, '', 'Kerema', 1, 92, ''),
(28, '', 'Kikori', 1, 92, ''),
(29, '', 'Kopiago', 1, 92, ''),
(30, '', 'Lake Murray', 1, 92, ''),
(31, '', 'Hela', 1, 93, ''),
(32, '', 'Komo-Margarima', 1, 93, ''),
(33, '', 'Koroba-Lake Kopiago', 1, 93, ''),
(34, '', 'Tari-Pori', 1, 93, ''),
(35, '', 'Anglimp-South Waghi', 1, 94, ''),
(36, '', 'Banz', 1, 94, ''),
(37, '', 'Jimi', 1, 94, ''),
(38, '', 'North Waghi', 1, 94, ''),
(39, '1406', 'Wosera Gawi District', 1, 90, ''),
(40, '1405', 'Yangoru-Saussia District', 1, 90, '');

-- --------------------------------------------------------

--
-- Table structure for table `adx_education`
--

CREATE TABLE `adx_education` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `color_code` varchar(20) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_education`
--

INSERT INTO `adx_education` (`id`, `name`, `icon`, `color_code`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(1, 'Gr. 1', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(2, 'Gr. 2', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(3, 'Gr. 3', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(4, 'Gr. 4', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(5, 'Gr. 5', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(6, 'Gr. 6', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(7, 'Gr. 7', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(8, 'Gr. 8', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(9, 'Gr. 9', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(10, 'Gr. 10', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(11, 'Gr. 11', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(12, 'Gr. 12', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(13, 'Vocational / TVET', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(14, 'College', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(15, 'University', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(16, 'No Formal Edu.', NULL, NULL, NULL, NULL, '2025-01-22 06:45:15', NULL, '2025-01-22 06:46:39');

-- --------------------------------------------------------

--
-- Table structure for table `adx_fertilizers`
--

CREATE TABLE `adx_fertilizers` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `color_code` varchar(20) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_fertilizers`
--

INSERT INTO `adx_fertilizers` (`id`, `name`, `icon`, `color_code`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(1, 'Nitrogen-Based Fertilizers', NULL, NULL, 'Examples: Urea, ammonium nitrate, and ammonium sulfate. Purpose: Boost plant growth, especially for leafy and green parts, as nitrogen is essential for protein and chlorophyll production.', NULL, '2024-11-20 11:37:26', NULL, '2024-11-20 11:37:26'),
(2, 'Phosphorus-Based Fertilizers', NULL, NULL, 'Examples: Superphosphate, monoammonium phosphate (MAP), and diammonium phosphate (DAP). Purpose: Promote root development, flowering, and fruiting, as phosphorus is essential for energy transfer and root growth.', NULL, '2024-11-20 11:37:26', NULL, '2024-11-20 11:37:26'),
(3, 'Potassium-Based Fertilizers', NULL, NULL, 'Examples: Potassium chloride (muriate of potash), potassium sulfate. Purpose: Enhance disease resistance, improve drought tolerance, and strengthen plant cell walls.', NULL, '2024-11-20 11:37:26', NULL, '2024-11-20 11:37:26'),
(4, 'NPK Blends (Complete Fertilizers)', NULL, NULL, 'Examples: 10-10-10 or 20-20-20 NPK formulations. Purpose: Provide a balanced mix of nitrogen, phosphorus, and potassium to support overall plant health and growth.', NULL, '2024-11-20 11:37:26', NULL, '2024-11-20 11:37:26'),
(5, 'Micronutrient Fertilizers', NULL, NULL, 'Examples: Zinc sulfate, iron chelates, copper sulfate. Purpose: Supply essential trace elements (e.g., iron, zinc, manganese) necessary for enzyme function and chlorophyll formation.', NULL, '2024-11-20 11:37:26', NULL, '2024-11-20 11:37:26'),
(6, 'Bio Fertilizer', '', '#06b15c', 'Using Bio Fertilizers like compost etc...', 2, '2024-12-02 14:47:52', 2, '2024-12-02 14:52:35');

-- --------------------------------------------------------

--
-- Table structure for table `adx_infections`
--

CREATE TABLE `adx_infections` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `color_code` varchar(20) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_infections`
--

INSERT INTO `adx_infections` (`id`, `name`, `icon`, `color_code`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(1, 'Insect Pests', NULL, NULL, 'Common pests include aphids, caterpillars, whiteflies, weevils, beetles, and grasshoppers, which cause damage by feeding on plants or transmitting diseases.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(2, 'Nematodes (Parasitic Worms)', NULL, NULL, 'Types include root-knot nematodes, cyst nematodes, and lesion nematodes, which attack roots, reducing nutrient uptake and causing stunted growth.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(3, 'Fungal Diseases', NULL, NULL, 'Examples include powdery mildew, downy mildew, rust, blight, fusarium wilt, and anthracnose, which affect leaves, stems, and fruits, often reducing photosynthesis and yield.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(4, 'Bacterial Diseases', NULL, NULL, 'Includes bacterial wilt, bacterial blight, fire blight, and soft rot, causing wilting, decay, and spots on leaves and stems.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(5, 'Viral Diseases', NULL, NULL, 'Notable viruses include mosaic viruses, tomato yellow leaf curl virus, banana bunchy top virus, and papaya ringspot virus, causing leaf mottling, stunted growth, and fruit deformities.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(6, 'Mites and Other Small Arthropods', NULL, NULL, 'Includes spider mites, broad mites, and thrips, which damage leaves and fruits, sometimes transmitting plant viruses.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(7, 'Rodents and Mammalian Pests', NULL, NULL, 'Rodents like rats and mice, as well as wild boars and deer, damage crops by consuming or uprooting plants.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(8, 'Bird Pests', NULL, NULL, 'Birds such as sparrows, crows, pigeons, and parakeets consume seeds and fruits, damaging crop yields.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02');

-- --------------------------------------------------------

--
-- Table structure for table `adx_livestock`
--

CREATE TABLE `adx_livestock` (
  `id` int(11) NOT NULL,
  `livestock_name` varchar(255) NOT NULL,
  `livestock_icon` varchar(255) DEFAULT NULL,
  `livestock_color_code` varchar(50) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_livestock`
--

INSERT INTO `adx_livestock` (`id`, `livestock_name`, `livestock_icon`, `livestock_color_code`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`) VALUES
(1, 'Cow', 'public/uploads/icons/1735851305_75de0f3630d9c15cd7f2.png', '#7c7979', 'The cow', 2, '2025-01-03 06:52:00', 2, '2025-01-03 06:55:05', 1),
(2, 'Pig', 'public/uploads/icons/1735851990_bd68b47267b671ceee7b.png', '#3c2525', '', 2, '2025-01-03 06:52:45', 2, '2025-01-03 07:06:30', 1),
(3, 'Goat', 'public/uploads/icons/1735852002_49a1397146e9004cbcc2.png', '#8289b0', '', 2, '2025-01-03 06:53:10', 2, '2025-01-03 07:06:42', 1);

-- --------------------------------------------------------

--
-- Table structure for table `adx_llg`
--

CREATE TABLE `adx_llg` (
  `id` int(11) NOT NULL,
  `llgcode` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `json_id` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_llg`
--

INSERT INTO `adx_llg` (`id`, `llgcode`, `name`, `country_id`, `province_id`, `district_id`, `json_id`) VALUES
(1, 'AB01', 'Ambunti LLG', 1, 90, 19, 'OCNPNG0030140101'),
(2, 'AB02', 'Dreikikier District', 1, 90, 19, 'OCNPNG0030140102'),
(3, 'WWK02', 'Wewak Island LLG', 1, 90, 22, 'OCNPNG0030140416');

-- --------------------------------------------------------

--
-- Table structure for table `adx_pesticides`
--

CREATE TABLE `adx_pesticides` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `color_code` varchar(20) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_pesticides`
--

INSERT INTO `adx_pesticides` (`id`, `name`, `icon`, `color_code`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(1, 'Herbicides', NULL, NULL, 'Chemicals specifically for killing or controlling weeds.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(2, 'Insecticides', NULL, NULL, 'Used to control or eliminate insects.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(3, 'Fungicides', NULL, NULL, 'Target and control fungal diseases.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(4, 'Rodenticides', NULL, NULL, 'Used to control rodents like rats and mice.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(5, 'Bactericides', NULL, NULL, 'Chemicals that control bacterial infections in plants.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(6, 'Nematicides', NULL, NULL, 'Used to control nematodes, which are harmful soil-dwelling worms.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(7, 'Acaricides', NULL, NULL, 'Used to kill mites and ticks.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(8, 'Molluscicides', NULL, NULL, 'Controls mollusks like snails and slugs.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(9, 'Growth Regulators', NULL, NULL, 'Chemicals that regulate the growth and development of plants.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(10, 'Defoliants', NULL, NULL, 'Cause plants to drop leaves, often used in cotton harvesting.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(11, 'Desiccants', NULL, NULL, 'Speed up the drying of plant tissues, used in the harvesting of crops.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(12, 'Repellents', NULL, NULL, 'Used to repel pests rather than kill them.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(13, 'Attractants', NULL, NULL, 'Used to attract pests to a certain area for control.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(14, 'Soil Fumigants', NULL, NULL, 'Chemicals applied to soil to control soil-borne pests and pathogens.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(15, 'Bio Pesticides', NULL, '#000000', 'Using bio pesticides and pest controls', 2, '2024-12-02 14:49:35', NULL, '2024-12-02 14:49:35');

-- --------------------------------------------------------

--
-- Table structure for table `adx_province`
--

CREATE TABLE `adx_province` (
  `id` int(11) NOT NULL,
  `provincecode` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `country_id` int(11) NOT NULL,
  `json_id` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_province`
--

INSERT INTO `adx_province` (`id`, `provincecode`, `name`, `country_id`, `json_id`) VALUES
(86, '03', 'Central Province', 1, ''),
(87, '10', 'Chimbu Province', 1, ''),
(88, '11', 'Eastern Highlands Province', 1, ''),
(89, '18', 'East New Britain Province', 1, ''),
(90, '14', 'East Sepik Province', 1, 'OCNPNG003014'),
(91, '08', 'Enga Province', 1, ''),
(92, '02', 'Gulf Province', 1, ''),
(93, '21', 'Hela Province', 1, ''),
(94, '22', 'Jiwaka Province', 1, ''),
(95, '13', 'Madang Province', 1, ''),
(96, '16', 'Manus Province', 1, ''),
(97, '05', 'Milne Bay Province', 1, ''),
(98, '12', 'Morobe Province', 1, ''),
(99, '17', 'New Ireland Province', 1, ''),
(100, '06', 'Oro - Northern Province', 1, ''),
(101, '07', 'Southern Highlands Province', 1, ''),
(102, '01', 'Western Province', 1, ''),
(103, '09', 'Western Highlands Province', 1, ''),
(104, '19', 'West New Britain Province', 1, ''),
(105, '20', 'AROB Bougainville', 1, 'OCNPNG004020');

-- --------------------------------------------------------

--
-- Table structure for table `adx_ward`
--

CREATE TABLE `adx_ward` (
  `id` int(11) NOT NULL,
  `wardcode` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `llg_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_ward`
--

INSERT INTO `adx_ward` (`id`, `wardcode`, `name`, `country_id`, `province_id`, `district_id`, `llg_id`) VALUES
(1, 1, 'Ward 1', 1, 90, 19, 1),
(2, 2, 'Ward 2', 1, 90, 19, 1),
(3, 140502, 'Ward 02', 1, 90, 22, 3),
(4, 140503, 'Ward 03', 1, 90, 22, 3);

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_blocks`
--

CREATE TABLE `crops_farm_blocks` (
  `id` int(11) NOT NULL,
  `farmer_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `block_code` varchar(50) NOT NULL,
  `org_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `llg_id` int(11) NOT NULL,
  `ward_id` int(11) NOT NULL,
  `village` varchar(100) NOT NULL,
  `block_site` varchar(200) NOT NULL,
  `lon` varchar(50) DEFAULT NULL,
  `lat` varchar(50) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `status` varchar(50) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_blocks`
--

INSERT INTO `crops_farm_blocks` (`id`, `farmer_id`, `crop_id`, `block_code`, `org_id`, `country_id`, `province_id`, `district_id`, `llg_id`, `ward_id`, `village`, `block_site`, `lon`, `lat`, `remarks`, `status`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(15, 4, 1, 'F707995-001', 2, 1, 90, 19, 1, 1, 'Avatip Village', 'Arere lo Wara', '143.590992', '-3.559612', 'Cool man', 'active', 17, '2024-11-09 09:28:32', 17, '2025-01-22 10:08:53'),
(16, 2, 4, 'F707995-002', 2, 1, 90, 19, 1, 2, 'Avatip Village', 'Arere lo Wara', '143.362490', '-3.962300', 'hththrth', 'active', 17, '2024-11-09 09:29:53', 17, '2025-01-10 10:22:52'),
(17, 4, 5, 'F799463-006', 2, 1, 90, 19, 1, 2, 'Avatip Village', 'Arere lo Wara', '145.611677', '-7.451650', '', 'active', 17, '2024-11-09 09:31:03', 17, '2024-11-30 06:00:25'),
(18, 3, 4, 'F712586-008', 2, 1, 90, 19, 1, 1, 'Avatip Village', 'Arere lo Wara', '145.229972', '-5.568096', '', 'active', 17, '2024-11-09 09:34:03', 17, '2024-11-28 06:51:08'),
(26, 2, 5, 'F707995-003', 2, 1, 90, 19, 1, 1, 'Villock', 'Nice Peles', '', '', 'thisfs dffk', 'active', 17, '2025-01-10 11:24:03', NULL, '2025-01-10 11:24:03'),
(27, 3, 4, 'F712586-009', 2, 1, 90, 19, 1, 2, 'Avatip Village', 'Arere lo Wara', '143.362490', '-3.962300', 'dasdajl', 'active', 17, '2025-01-10 11:25:25', NULL, '2025-01-10 11:25:25'),
(28, 5, 5, 'F790802-001', 2, 1, 90, 22, 3, 3, 'Wewak Section', 'Island Stret', '', '', '', 'active', 17, '2025-01-22 06:49:22', NULL, '2025-01-22 06:49:22');

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_crops_data`
--

CREATE TABLE `crops_farm_crops_data` (
  `id` int(11) NOT NULL,
  `block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `action_type` enum('add','remove') NOT NULL,
  `action_reason` varchar(100) NOT NULL,
  `number_of_plants` int(11) NOT NULL,
  `breed` varchar(255) DEFAULT NULL,
  `action_date` date NOT NULL,
  `hectares` decimal(10,2) NOT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `status` enum('active','inactive','deleted') NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_crops_data`
--

INSERT INTO `crops_farm_crops_data` (`id`, `block_id`, `crop_id`, `action_type`, `action_reason`, `number_of_plants`, `breed`, `action_date`, `hectares`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`) VALUES
(4, 11, 47, 'add', 'new planting', 23, 'German', '2024-10-30', 12.00, 'this si the test', 17, '2024-11-09 17:04:05', NULL, '2024-11-09 17:04:05', 'active'),
(5, 11, 47, 'add', 'new planting', 23, 'German', '2024-10-30', 12.00, 'this si the test', 17, '2024-11-09 17:06:11', 17, '2024-11-09 17:47:46', 'active'),
(6, 11, 47, 'add', 'new planting', 5, 'German', '2024-11-05', 5.00, '', 17, '2024-11-09 17:08:05', 17, '2024-11-09 17:48:20', 'active'),
(7, 11, 47, 'remove', 'disease', 23, 'German', '2024-11-06', 5.00, 'This are the disease affected crops', 17, '2024-11-09 17:10:59', NULL, '2024-11-09 17:10:59', 'active'),
(8, 11, 47, 'remove', 'disaster', 10, 'German', '2024-10-29', 9.00, 'this is the disaster', 17, '2024-11-09 17:28:05', NULL, '2024-11-09 17:28:05', 'active'),
(9, 15, 47, 'add', 'new planting', 7, 'HiBreed', '2024-10-29', 5.00, 'This si the remarks', 17, '2024-11-10 13:35:49', 17, '2024-11-10 13:36:08', 'active'),
(10, 15, 47, 'add', 'new planting', 14, 'German', '2024-11-06', 13.00, 'New Clearance extension', 17, '2024-11-10 13:37:35', NULL, '2024-11-10 13:37:35', 'active'),
(11, 15, 47, 'remove', 'natural disaster', 3, 'German', '2024-10-26', 0.20, 'a small landslide', 17, '2024-11-10 17:30:24', NULL, '2024-11-10 17:30:24', 'active'),
(12, 15, 1, 'remove', 'Disease Infection', 34, 'German', '2024-10-30', 12.00, 'This is the remarks', 17, '2024-11-28 05:30:17', NULL, '2024-11-28 05:30:17', 'active'),
(13, 15, 1, 'add', 'new planting', 17, 'German', '2024-10-05', 24.00, 'This is the remarks', 17, '2024-11-28 05:37:47', 17, '2024-11-29 06:59:13', 'active'),
(14, 15, 1, 'remove', 'Disease Infection', 34, 'German', '2024-11-15', 9.00, 'thisfddf', 17, '2024-11-28 05:44:01', 17, '2024-11-28 05:49:05', 'active'),
(15, 15, 1, 'remove', 'Disease Infection', 11, 'Samoan', '2024-11-17', 44.00, 'Remrem', 17, '2024-11-28 06:19:33', NULL, '2024-11-28 06:19:33', 'active'),
(16, 15, 1, 'add', 'Prund', 67, 'Pop', '2024-10-29', 6.80, 'afdfk', 17, '2024-11-28 06:21:33', 17, '2024-11-29 06:58:32', 'active'),
(17, 18, 4, 'remove', 'Disease Infection', 12, 'Arabika', '2024-07-10', 5.00, 'dafnpkdsnflk', 17, '2024-11-29 07:20:01', NULL, '2024-11-29 07:20:01', 'active'),
(18, 18, 4, 'remove', 'Disease Infection', 23, 'Hybrid', '2024-08-10', 16.00, 'Tifsdofhisdhf', 17, '2024-11-29 07:54:31', NULL, '2024-11-29 07:54:31', 'active'),
(19, 16, 0, 'add', 'new planting', 12, 'Arabika', '2025-01-05', 5.00, 'This is my new extension for this coffee block', 17, '2025-01-10 10:24:37', 17, '2025-01-10 10:45:35', 'active'),
(20, 16, 4, 'add', 'new planting', 9, 'Arabika', '2025-01-07', 10.00, 'this is new extension', 17, '2025-01-10 10:36:48', NULL, '2025-01-10 10:36:48', 'active'),
(21, 15, 4, 'remove', 'Disease Infection', 110, 'Arabika', '2025-01-07', 10.00, 'hththrth', 17, '2025-01-10 11:44:05', NULL, '2025-01-10 11:44:05', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_disease_data`
--

CREATE TABLE `crops_farm_disease_data` (
  `id` int(11) NOT NULL,
  `block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `disease_type` varchar(255) NOT NULL,
  `disease_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `action_reason` text DEFAULT NULL,
  `number_of_plants` int(11) DEFAULT 0,
  `breed` varchar(255) DEFAULT NULL,
  `action_date` datetime DEFAULT NULL,
  `hectares` decimal(10,2) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(50) DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_disease_data`
--

INSERT INTO `crops_farm_disease_data` (`id`, `block_id`, `crop_id`, `disease_type`, `disease_name`, `description`, `action_reason`, `number_of_plants`, `breed`, `action_date`, `hectares`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`) VALUES
(1, 15, 1, 'Insect Pests', 'Cocoa Borer', 'This is the disease', 'Disease Infection', 17, 'German', '2024-10-05 00:00:00', 24.00, 'This is the remarks', 17, '2024-11-28 05:30:17', 17, '2024-11-28 06:21:50', 'active'),
(2, 15, 1, 'Bacterial Diseases', 'Cocoa Borer', 'thisis ss', 'Disease Infection', 34, 'German', '2024-11-15 00:00:00', 9.00, 'thisfddf', 17, '2024-11-28 05:44:01', 17, '2024-11-28 05:49:05', 'active'),
(3, 15, 1, 'Viral Diseases', 'BoraBora', 'This infe', 'Disease Infection', 11, 'Samoan', '2024-11-17 00:00:00', 44.00, 'Remrem', 17, '2024-11-28 06:19:33', NULL, '2024-11-28 06:19:33', 'active'),
(4, 15, 1, 'Mites and Other Small Arthropods', 'Binatang', 'tisfhisdhis', 'Disease Infection', 67, 'Pop', '2024-10-29 00:00:00', 6.80, 'afdfk', 17, '2024-11-28 06:21:33', NULL, '2024-11-28 06:21:33', 'active'),
(5, 18, 4, 'Fungal Diseases', 'Coffee Fungi', 'Tdsfdfpsdfm', 'Disease Infection', 12, 'Arabika', '2024-07-10 00:00:00', 5.00, 'dafnpkdsnflk', 17, '2024-11-29 07:20:01', NULL, '2024-11-29 07:20:01', 'active'),
(6, 18, 4, 'Bird Pests', 'Flower birds', 'TSFSDfojsdfp', 'Disease Infection', 23, 'Hybrid', '2024-08-10 00:00:00', 16.00, 'Tifsdofhisdhf', 17, '2024-11-29 07:54:31', NULL, '2024-11-29 07:54:31', 'active'),
(7, 15, 4, 'Viral Diseases', 'Cocoa Borer', 'This is the dry coffee', 'Disease Infection', 110, 'Arabika', '2025-01-07 00:00:00', 10.00, 'hththrth', 17, '2025-01-10 11:44:05', NULL, '2025-01-10 11:44:05', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_fertilizer_data`
--

CREATE TABLE `crops_farm_fertilizer_data` (
  `id` int(11) NOT NULL,
  `block_id` int(11) NOT NULL,
  `fertilizer_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `brand` varchar(255) DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `unit` decimal(10,2) DEFAULT NULL,
  `quantity` decimal(10,2) DEFAULT NULL,
  `action_date` date DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_fertilizer_data`
--

INSERT INTO `crops_farm_fertilizer_data` (`id`, `block_id`, `fertilizer_id`, `crop_id`, `name`, `brand`, `unit_of_measure`, `unit`, `quantity`, `action_date`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`) VALUES
(1, 17, 3, 3, 'Posta', 'Cookf', 'l', NULL, 20.00, '2024-11-12', 'Use this with care', 17, '2024-11-10 14:39:01', 17, '2024-11-10 14:57:53', 'active'),
(2, 17, 3, 3, 'Posta', 'Cookf', 'ml', NULL, 20.00, '2024-11-05', 'This is rema', 17, '2024-11-10 14:42:22', NULL, '2024-11-10 14:42:22', 'active'),
(3, 15, 1, 1, 'Posta', 'Ppop', 'l', 23.00, 20.00, '2024-10-30', 'dasdjpasj', 17, '2024-11-20 15:36:47', 17, '2024-11-20 15:39:34', 'active'),
(4, 17, 5, 5, 'Posta', 'Gramoxin', 'ml', 32.00, 5.00, '2025-01-08', 'This is gramox', 17, '2025-01-10 11:59:18', 17, '2025-01-10 11:59:47', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_harvest_data`
--

CREATE TABLE `crops_farm_harvest_data` (
  `id` int(11) NOT NULL,
  `block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `item` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `unit` decimal(10,2) DEFAULT NULL,
  `quantity` decimal(10,2) DEFAULT NULL,
  `harvest_date` date DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(50) DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_harvest_data`
--

INSERT INTO `crops_farm_harvest_data` (`id`, `block_id`, `crop_id`, `item`, `description`, `unit_of_measure`, `unit`, `quantity`, `harvest_date`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`) VALUES
(1, 17, 3, 'Rubber Cups', 'This is the rubber cups', 'cups', 2.00, 30.00, '2024-11-06', 'This si the cup', 17, '2024-11-10 18:16:11', 17, '2024-11-10 18:27:00', 'active'),
(2, 15, 1, 'Wet Beans', 'Pure wet beans', 'Bucket', 10.00, 20.00, '2024-10-30', 'This is the new beans', 17, '2024-11-20 15:46:51', 17, '2024-11-20 15:48:07', 'active'),
(3, 16, 4, 'Parchment', 'Tfsdifosh', 'Bucket', 12.00, 24.00, '2024-05-23', 'Thisfisdf mfldsfml', 17, '2024-11-29 07:45:02', NULL, '2024-11-29 07:45:02', 'active'),
(4, 16, 4, 'Parchment', 'AFDSfsdpj', 'Bilum', 9.00, 33.00, '2024-05-10', 'DFSDfkoj', 17, '2024-11-29 07:46:27', NULL, '2024-11-29 07:46:27', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_marketing_data`
--

CREATE TABLE `crops_farm_marketing_data` (
  `id` int(11) NOT NULL,
  `farmer_id` int(11) NOT NULL,
  `block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `llg_id` int(11) NOT NULL,
  `market_date` date DEFAULT NULL,
  `market_stage` varchar(100) DEFAULT NULL,
  `buyer_id` int(11) DEFAULT NULL,
  `selling_location` varchar(255) DEFAULT NULL,
  `product` varchar(255) NOT NULL,
  `product_type` varchar(200) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `unit` decimal(10,2) DEFAULT NULL,
  `quantity` decimal(10,2) DEFAULT NULL,
  `market_price_per_unit` decimal(10,2) DEFAULT NULL,
  `total_freight_cost` decimal(10,2) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_marketing_data`
--

INSERT INTO `crops_farm_marketing_data` (`id`, `farmer_id`, `block_id`, `crop_id`, `country_id`, `province_id`, `district_id`, `llg_id`, `market_date`, `market_stage`, `buyer_id`, `selling_location`, `product`, `product_type`, `description`, `unit_of_measure`, `unit`, `quantity`, `market_price_per_unit`, `total_freight_cost`, `remarks`, `status`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(1, 4, 0, 5, 0, 0, 0, 0, '2024-11-07', 'primary', 2, 'Maprik', 'Rubber Cups', 'Green Beans', 'Description', 'kg', 2.00, 30.00, 5.50, 200.00, 'REmarkses', 'active', 17, '2024-11-10 20:32:04', 17, '2025-01-02 11:59:43'),
(2, 4, 0, 4, 0, 0, 0, 0, '2024-11-06', 'harvest', 4, NULL, 'Coffee Bags', 'Parchment', 'this is the description', 'g', 25.00, 2.00, 200.00, NULL, 'this is remarks', 'active', 17, '2024-11-18 10:38:49', 17, '2025-01-02 11:37:55'),
(3, 4, 0, 1, 0, 0, 0, 0, '2024-09-10', 'primary', 11, NULL, 'Dry Beans', 'Dry Beans', 'fjodfjdpajf', 'kg', 63.40, 12.00, 800.00, NULL, 'fsdpfhdsifh', 'active', 17, '2024-11-29 07:32:42', NULL, '2024-11-29 07:32:42'),
(4, 4, 0, 5, 0, 0, 0, 0, '2024-10-17', 'primary', 2, '', 'Cup', 'Cuping', 'adfkndsl', 'kg', 1.00, 20.00, 15.00, 0.00, 'Cooking monkey', 'active', 17, '2024-11-29 07:34:11', 19, '2025-01-07 12:13:04'),
(5, 3, 0, 4, 0, 0, 0, 0, '2023-05-06', 'secondary', 5, '', 'Tadafor', 'Dadsa', 'cafdasdfer', 'l', 3.00, 30.00, 3.40, 400.00, 'asdasm;', 'active', 19, '2025-01-07 12:27:21', 19, '2025-01-07 13:27:11'),
(6, 2, 0, 4, 0, 0, 0, 0, '2025-01-09', 'harvest', 2, 'Maprik', 'Dry Coffee', 'Dried', 'This is the dry coffee', 'kg', 50.00, 12.00, 5.00, 200.00, 'This is the wanway coffee', 'active', 17, '2025-01-10 10:50:58', NULL, '2025-01-10 10:50:58');

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_pesticides_data`
--

CREATE TABLE `crops_farm_pesticides_data` (
  `id` int(11) NOT NULL,
  `block_id` int(11) NOT NULL,
  `pesticide_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `brand` varchar(255) DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `unit` decimal(10,2) DEFAULT NULL,
  `quantity` decimal(10,2) DEFAULT NULL,
  `action_date` date DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_pesticides_data`
--

INSERT INTO `crops_farm_pesticides_data` (`id`, `block_id`, `pesticide_id`, `crop_id`, `name`, `brand`, `unit_of_measure`, `unit`, `quantity`, `action_date`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`) VALUES
(1, 17, 3, 3, 'Run insects', 'Kuku ', 'kg', NULL, 20.00, '2024-10-29', 'This si how much i applied', 17, '2024-11-10 15:13:53', 17, '2024-11-19 04:35:20', 'active'),
(2, 15, 6, 1, 'Gramoxen', 'Gramo', 'l', 20.00, 2.00, '2024-10-30', 'dasdjpasj', 17, '2024-11-20 15:04:33', 17, '2024-11-20 15:40:37', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_tools`
--

CREATE TABLE `crops_farm_tools` (
  `id` int(11) NOT NULL,
  `tool_type` enum('general','speciality') NOT NULL,
  `crop_id` int(11) DEFAULT NULL,
  `tool_name` varchar(255) NOT NULL,
  `remarks` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` enum('active','inactive') DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `crop_buyers`
--

CREATE TABLE `crop_buyers` (
  `id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `buyer_code` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `address` text DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `operation_span` enum('local','national') NOT NULL,
  `location_id` int(11) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` enum('active','inactive') DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crop_buyers`
--

INSERT INTO `crop_buyers` (`id`, `crop_id`, `buyer_code`, `name`, `address`, `contact_number`, `email`, `operation_span`, `location_id`, `description`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`) VALUES
(1, 1, 'CB1001', 'Green Harvest Ltd.', '123 Green Lane, Springfield', '+123456789', '<EMAIL>', 'local', 90, 'A trusted buyer specializing in organic crops.', 17, '2023-05-10 00:00:00', 17, '2024-11-20 11:13:09', 'active'),
(2, 4, 'CB1002', 'National Produce Co.', '456 Market Road, Riverside', '+234567890', '<EMAIL>', 'national', 1, 'Nationwide distributor of fresh produce.', 17, '2023-06-15 00:00:00', 17, '2025-01-02 14:19:32', 'active'),
(3, 1, 'CB1003', 'Harvest Hub', '789 Rural Ave, Oakville', '+345678901', '<EMAIL>', 'local', 90, 'Small-scale buyer focusing on local markets.', 17, '2023-07-20 00:00:00', 17, '2025-01-02 14:19:42', 'active'),
(4, 5, 'CB1004', 'AgriGlobal Exporters', '101 Export Plaza, Capital City', '+456789012', '<EMAIL>', 'national', 1, 'Leading exporter of agricultural products.', 17, '2023-08-25 00:00:00', 17, '2025-01-02 14:19:53', 'active'),
(5, 5, 'CB1005', 'Farmers Direct', '23 Main Street, Smalltown', '+567890123', '<EMAIL>', 'local', 90, 'Focused on buying directly from small farmers.', 17, '2023-09-30 00:00:00', 17, '2025-01-02 14:20:05', 'active'),
(10, 45, 'CB1006', 'National Produce Co.', 'ffsdfdsfds', '+234567890', '<EMAIL>', 'national', NULL, '', 17, '2024-11-11 10:45:23', 17, '2024-11-11 10:49:26', 'active'),
(11, 45, 'CB1007', 'Eliven', 'Eliven Maprik', '556555', '<EMAIL>', 'local', NULL, 'Raw Tiss  ', 17, '2024-11-11 10:48:34', 17, '2024-11-11 10:49:34', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `crop_processors`
--

CREATE TABLE `crop_processors` (
  `id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `stage` enum('pre-harvest','harvest','post-harvest') NOT NULL,
  `processor_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crop_processors`
--

INSERT INTO `crop_processors` (`id`, `crop_id`, `stage`, `processor_name`, `description`, `created_at`, `updated_at`) VALUES
(1, 1, 'post-harvest', 'furmentry', 'Dryer', '2023-02-10 00:00:00', '2024-11-11 09:42:51'),
(2, 1, 'harvest', 'pod slicer', 'pod handler', '2023-04-15 00:00:00', '2024-11-11 09:43:03'),
(3, 1, 'pre-harvest', 'cocoa prunner', 'prunist', '2023-06-20 00:00:00', '2024-11-11 09:43:13');

-- --------------------------------------------------------

--
-- Table structure for table `dakoii_org`
--

CREATE TABLE `dakoii_org` (
  `id` int(11) UNSIGNED NOT NULL,
  `orgcode` varchar(500) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `addlockprov` varchar(100) NOT NULL,
  `addlockcountry` varchar(100) NOT NULL,
  `orglogo` varchar(200) NOT NULL,
  `is_locationlocked` tinyint(1) NOT NULL DEFAULT 0,
  `province_json` varchar(255) NOT NULL,
  `district_json` varchar(255) NOT NULL,
  `llg_json` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `license_status` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `dakoii_org`
--

INSERT INTO `dakoii_org` (`id`, `orgcode`, `name`, `description`, `addlockprov`, `addlockcountry`, `orglogo`, `is_locationlocked`, `province_json`, `district_json`, `llg_json`, `is_active`, `license_status`, `created_at`, `updated_at`) VALUES
(2, '2345', 'East Sepik Provincial Administration', 'This is East Sepik Provincial Administration', '90', '1', 'public/uploads/org_logo/2345_1730809835.png', 0, '', '', '', 1, 'paid', '2023-03-16 06:49:23', '2024-11-05 12:30:35'),
(3, '49501', 'Cooking', 'This is cooking descript', '', '', 'http://localhost/promis/public/uploads/org_logo/49501_1679908153.jpg', 0, '', '', '', 0, '', '2023-03-27 09:09:13', '2023-03-27 09:09:13'),
(4, '25492', 'Rico', 'Tekorif', '', '', 'http://localhost/promis/public/uploads/org_logo/25492_1679966568.png', 0, '', '', '', 0, '', '2023-03-27 09:15:40', '2023-03-28 01:22:48'),
(5, '16807', 'Activate', '', '', '', '', 0, '', '', '', 1, '', '2023-03-27 09:19:12', '2023-03-27 09:19:12'),
(6, '53874', 'Oepn Org', 'This Oepn thisdfnfsdj', '', '', 'http://localhost/promis/public/uploads/org_logo/53874_1679914956.jpg', 0, '', '', '', 1, '', '2023-03-27 09:23:18', '2023-03-27 11:02:36'),
(7, '82751', 'Souths PA', 'Southern Highlands Prov Admin', '', '', 'http://localhost/selsys/public/uploads/org_logo/82751_1695260285.png', 0, '', '', '', 1, '', '2023-09-21 01:38:05', '2023-09-21 01:38:05'),
(8, '84261', 'Enga Provincial Administration', '', '', '', 'http://localhost/selsys/public/uploads/org_logo/84261_1719814946.png', 0, '', '', '', 1, '', '2024-07-01 06:22:26', '2024-07-01 06:22:26');

-- --------------------------------------------------------

--
-- Table structure for table `dakoii_users`
--

CREATE TABLE `dakoii_users` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `orgcode` varchar(500) NOT NULL,
  `role` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `dakoii_users`
--

INSERT INTO `dakoii_users` (`id`, `name`, `username`, `password`, `orgcode`, `role`, `is_active`, `created_at`, `updated_at`) VALUES
(2, 'Free Kenny', 'fkenny', '$2y$10$A.8jXDJcv/wbzVi3l8bt/OPY6B0FpExgbUg.HOk6Khq9CYvKNQCyK', '', 'dakoii', 1, '2023-03-16 06:49:23', '2024-07-04 08:27:10');

-- --------------------------------------------------------

--
-- Table structure for table `farmers_children`
--

CREATE TABLE `farmers_children` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `farmer_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `date_of_birth` date NOT NULL,
  `gender` enum('Male','Female') NOT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `farmers_children`
--

INSERT INTO `farmers_children` (`id`, `org_id`, `farmer_id`, `name`, `date_of_birth`, `gender`, `created_by`, `created_at`, `updated_at`, `updated_by`) VALUES
(2, 2, 2, 'Baby Ambunti', '2010-10-29', 'Male', NULL, '2024-11-05 06:32:13', '2024-11-05 07:58:00', NULL),
(3, 2, 2, 'Ambunti Kid 2', '2015-10-29', 'Female', NULL, '2024-11-05 07:58:23', '2024-11-05 07:58:23', NULL),
(5, 2, 3, 'Wan Child', '2007-03-20', 'Male', NULL, '2024-11-27 21:32:21', '2024-11-27 21:32:21', NULL),
(6, 2, 3, 'Kool', '2023-11-07', 'Female', NULL, '2024-11-27 22:04:33', '2024-11-27 22:04:46', 17),
(7, 2, 1, 'Rekrek Pikin', '2002-12-30', 'Female', NULL, '2025-01-02 02:24:59', '2025-01-02 02:24:59', NULL),
(8, 2, 1, 'Wewak Kulablia', '2010-01-04', 'Male', NULL, '2025-01-02 02:25:20', '2025-01-02 02:25:20', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `farmer_information`
--

CREATE TABLE `farmer_information` (
  `id` int(11) NOT NULL,
  `org_id` int(11) DEFAULT NULL,
  `farmer_code` varchar(20) NOT NULL,
  `given_name` varchar(50) NOT NULL,
  `surname` varchar(50) NOT NULL,
  `date_of_birth` date NOT NULL,
  `gender` enum('Male','Female') NOT NULL,
  `village` varchar(100) DEFAULT NULL,
  `ward_id` int(11) DEFAULT NULL,
  `llg_id` int(11) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `province_id` int(11) DEFAULT NULL,
  `country_id` int(11) DEFAULT 1,
  `phone` varchar(200) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `marital_status` enum('Single','Married','Divorce','Widow','De-facto') NOT NULL,
  `highest_education_id` int(11) DEFAULT NULL,
  `course_taken` varchar(255) DEFAULT NULL,
  `id_photo` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `farmer_information`
--

INSERT INTO `farmer_information` (`id`, `org_id`, `farmer_code`, `given_name`, `surname`, `date_of_birth`, `gender`, `village`, `ward_id`, `llg_id`, `district_id`, `province_id`, `country_id`, `phone`, `email`, `address`, `marital_status`, `highest_education_id`, `course_taken`, `id_photo`, `created_at`, `created_by`, `updated_at`, `updated_by`, `status`) VALUES
(1, 2, 'F725099', 'Maku', 'Bata', '1990-11-05', 'Male', 'Ambunti Village', 2, 2, 19, 90, 1, '755555', '<EMAIL>', 'Address Lives at Ambunti Station', 'Single', 12, 'This coourse Tken', '', '2024-11-05 05:43:29', 17, '2025-01-06 02:54:08', 19, 'inactive'),
(2, 2, 'F707995', 'Cool Mix', 'Bata', '1982-11-06', 'Male', 'Ambunti Village', 2, 1, 19, 90, 1, '755555', '<EMAIL>', 'This is the address', 'Divorce', 12, 'High School Education', 'public/uploads/farmer_photos/1730806392_3fad0beda40c5c306ef8.jpg', '2024-11-05 05:51:31', 17, '2024-11-28 22:04:20', 17, 'active'),
(3, 2, 'F712586', 'Cool', 'Mangi', '1994-11-05', 'Female', 'Avatip Village', 2, 1, 19, 90, 1, '2534645', '<EMAIL>', 'Along the Sepik River', 'Married', 0, 'Sustainable Farms', 'public/uploads/farmer_photos/1731068924_e2227fbaaeea867de774.jpg', '2024-11-08 12:28:44', 17, '2024-11-27 21:46:20', 17, 'active'),
(4, 2, 'F799463', 'Wanda Gates', 'Gunim', '2004-10-30', 'Male', 'Wandex Village', 1, 1, 19, 90, 1, '7203293', '<EMAIL>', 'This is the addressesss', 'Widow', 10, 'Takin Too Courses', 'public/uploads/farmer_photos/1731069200_4c860a579b3ae29c310e.png', '2024-11-08 12:33:20', 17, '2024-11-28 21:57:05', 17, 'active'),
(5, 2, 'F790802', 'Wewak', 'Mangi', '2002-01-08', 'Male', 'Biam Island', 4, 3, 22, 90, 1, '', '', '', 'Single', 16, '', 'public/uploads/farmer_photos/1737492487_c4d901db19ad2817caa7.jpg', '2025-01-21 20:48:07', 17, '2025-01-21 20:48:07', NULL, 'active');

-- --------------------------------------------------------

--
-- Table structure for table `groupings`
--

CREATE TABLE `groupings` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `created_by` varchar(200) DEFAULT NULL,
  `updated_by` varchar(200) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `groupings`
--

INSERT INTO `groupings` (`id`, `org_id`, `name`, `description`, `parent_id`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(2, 2, 'Group one', 'This group one descrtion', NULL, 'minad', NULL, '2024-11-01 05:53:49', '2024-11-01 05:53:49'),
(3, 2, 'Group 1A', 'This is the one A', 2, 'minad', NULL, '2024-11-01 05:56:47', '2024-11-01 05:56:47'),
(4, 2, 'Group 1B', 'This is 1B', 2, 'minad', 'minad', '2024-11-01 05:57:23', '2024-11-01 05:57:37'),
(5, 2, 'Kukul Corporative', 'This is the Kukul Group', NULL, 'Minad', NULL, '2025-01-04 08:53:43', '2025-01-04 08:53:43');

-- --------------------------------------------------------

--
-- Table structure for table `livestock_farm_blocks`
--

CREATE TABLE `livestock_farm_blocks` (
  `id` int(11) NOT NULL,
  `farmer_id` int(11) NOT NULL,
  `block_code` varchar(50) NOT NULL,
  `org_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `llg_id` int(11) NOT NULL,
  `ward_id` int(11) NOT NULL,
  `village` varchar(100) NOT NULL,
  `block_site` varchar(200) NOT NULL,
  `lon` varchar(50) DEFAULT NULL,
  `lat` varchar(50) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `status` varchar(50) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `livestock_farm_blocks`
--

INSERT INTO `livestock_farm_blocks` (`id`, `farmer_id`, `block_code`, `org_id`, `country_id`, `province_id`, `district_id`, `llg_id`, `ward_id`, `village`, `block_site`, `lon`, `lat`, `remarks`, `status`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(15, 3, 'LSF712586-010', 2, 1, 90, 19, 1, 2, 'Avatip Village', 'Nice Peles', '143.590992', '-3.559612', 'Cool man this is the cool part', 'inactive', 17, '2024-11-09 09:28:32', 17, '2025-01-03 06:13:31'),
(16, 4, 'LSF799463-009', 2, 1, 90, 22, 3, 4, 'Mushu Island', 'Mushu Bay', '143.362490', '-3.962300', 'hththrth', 'active', 17, '2024-11-09 09:29:53', 17, '2025-01-03 08:40:47'),
(17, 2, 'LSF707995-001', 2, 1, 90, 19, 1, 2, 'Avatip Village', 'Arere lo Wara', '145.611677', '-7.451650', '', 'active', 17, '2024-11-09 09:31:03', 17, '2025-01-03 06:14:27'),
(18, 4, 'LSF799463-008', 2, 1, 90, 19, 1, 1, 'Avatip Village', 'Arere lo Wara', '145.229972', '-5.568096', 'fadsve', 'active', 17, '2024-11-09 09:34:03', 17, '2025-01-03 06:22:31'),
(19, 3, 'LSF712586-011', 2, 1, 90, 22, 3, 4, 'One Village', 'One block', '', '', '', 'active', 19, '2025-01-07 13:39:53', NULL, '2025-01-07 13:39:53');

-- --------------------------------------------------------

--
-- Table structure for table `livestock_farm_data`
--

CREATE TABLE `livestock_farm_data` (
  `id` int(11) NOT NULL,
  `block_id` int(11) NOT NULL,
  `livestock_id` int(11) NOT NULL,
  `breed` varchar(255) NOT NULL,
  `he_total` int(11) DEFAULT 0,
  `she_total` int(11) DEFAULT 0,
  `pasture_type` varchar(255) DEFAULT NULL,
  `growth_stage` varchar(255) DEFAULT NULL,
  `cost_per_livestock` varchar(20) NOT NULL,
  `low_price_per_livestock` varchar(20) NOT NULL,
  `high_price_per_livestock` varchar(20) NOT NULL,
  `comments` text DEFAULT NULL,
  `action_date` datetime DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(50) DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `livestock_farm_data`
--

INSERT INTO `livestock_farm_data` (`id`, `block_id`, `livestock_id`, `breed`, `he_total`, `she_total`, `pasture_type`, `growth_stage`, `cost_per_livestock`, `low_price_per_livestock`, `high_price_per_livestock`, `comments`, `action_date`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`) VALUES
(1, 18, 1, 'Braman', 1, 23, 'open pasture', 'calf', '34.50', '50.05', '60.00', 'These are locally breed', '2025-01-01 00:00:00', 17, '2025-01-03 07:33:14', 17, '2025-01-03 08:09:08', 'active'),
(2, 18, 2, 'Local ', 4, 2, 'free-range', 'adult', '250', '24', '400', 'local pigs', '2024-12-30 00:00:00', 17, '2025-01-03 07:43:27', 17, '2025-01-03 08:02:45', 'active'),
(3, 16, 3, 'High breed', 3, 4, 'barn', 'adult', '202', '34', '50', 'This isi sh ', '2024-12-20 00:00:00', 17, '2025-01-03 08:43:39', NULL, '2025-01-03 08:43:39', 'active'),
(4, 17, 2, 'ples', 2, 5, 'open pasture', 'adult', '200', '700', '1200', 'This is hte pigs ples wan', '2025-01-08 00:00:00', 17, '2025-01-10 10:47:43', NULL, '2025-01-10 10:47:43', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `livestock_production_data`
--

CREATE TABLE `livestock_production_data` (
  `id` int(11) NOT NULL,
  `livestock_id` int(11) NOT NULL,
  `item` varchar(255) NOT NULL,
  `unit_of_measure` varchar(50) NOT NULL,
  `unit` decimal(10,2) NOT NULL,
  `price_per_unit` decimal(10,2) DEFAULT NULL,
  `cost_per_unit` decimal(10,2) DEFAULT NULL,
  `quantity` int(11) DEFAULT 0,
  `clients` text DEFAULT NULL,
  `action_date` datetime DEFAULT current_timestamp(),
  `comments` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(50) DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `permissions_items`
--

CREATE TABLE `permissions_items` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `permission_code` varchar(255) NOT NULL,
  `permission_text` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `permissions_sets`
--

CREATE TABLE `permissions_sets` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `permission_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `permissions_user_districts`
--

CREATE TABLE `permissions_user_districts` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `org_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `default_district` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `permissions_user_districts`
--

INSERT INTO `permissions_user_districts` (`id`, `org_id`, `user_id`, `district_id`, `default_district`, `created_at`, `created_by`, `updated_at`, `updated_by`) VALUES
(1, 2, 19, 22, 0, '2025-01-05 07:53:11', 14, '2025-01-05 08:17:40', 14),
(2, 2, 19, 20, 0, '2025-01-05 07:53:42', 14, '2025-01-05 08:17:40', NULL),
(3, 2, 19, 19, 1, '2025-01-05 08:17:40', 14, '2025-01-05 08:17:40', NULL),
(4, 2, 17, 19, 1, '2025-01-10 00:09:21', 14, '2025-01-10 00:09:21', NULL),
(5, 2, 17, 20, 0, '2025-01-10 01:03:05', 14, '2025-01-10 01:03:05', NULL),
(6, 2, 17, 22, 0, '2025-01-10 01:04:05', 14, '2025-01-10 01:04:05', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `plans`
--

CREATE TABLE `plans` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_code` varchar(50) NOT NULL,
  `plan_title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `plan_type` enum('corporate','development') NOT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` enum('active','inactive') DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans`
--

INSERT INTO `plans` (`id`, `org_id`, `plan_code`, `plan_title`, `description`, `start_date`, `end_date`, `plan_type`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`) VALUES
(2, 2, 'ESPIPDP2018-2028', 'East Sepik Province Integrated Development Plan 2018 - 2028', 'This is the plan', '2018-01-01', '2028-12-31', 'development', NULL, NULL, '2024-11-11 11:52:10', NULL, '2024-11-12 10:05:08', 'active'),
(3, 2, 'ESPACP2020-2023', 'East Sepik Provincial Administration Corporate Plan 2020-2023', 'This is the corporate plan', '2020-01-01', '2023-12-31', 'corporate', NULL, NULL, '2024-11-11 20:08:07', NULL, '2024-11-12 10:04:54', 'active'),
(4, 2, 'MTDP4', 'Medium Term Development Plan 4', 'This is the development plan', '2024-11-08', '2024-11-10', 'development', NULL, NULL, '2024-11-12 08:37:45', NULL, '2024-11-12 10:05:11', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `plans_deliverables`
--

CREATE TABLE `plans_deliverables` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `program_id` int(11) NOT NULL,
  `deliverable_code` varchar(50) NOT NULL,
  `deliverables` text NOT NULL,
  `target_outcome` text DEFAULT NULL,
  `target_date` date DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_deliverables`
--

INSERT INTO `plans_deliverables` (`id`, `org_id`, `plan_id`, `program_id`, `deliverable_code`, `deliverables`, `target_outcome`, `target_date`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 2, 2, 3, 'DEL002', 'This is the deliverable', 'This is the target outcome', '2024-11-08', NULL, 14, '2024-11-11 16:46:56', '2024-11-11 17:50:32'),
(2, 2, 2, 3, 'DEL003', 'This is the deliverable 2', 'Complete this delvierable 2', '2025-11-06', 14, NULL, '2024-11-11 18:49:47', '2024-11-11 18:49:47');

-- --------------------------------------------------------

--
-- Table structure for table `plans_indicators`
--

CREATE TABLE `plans_indicators` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `program_id` int(11) NOT NULL,
  `indicator_code` varchar(50) NOT NULL,
  `indicator_name` varchar(255) NOT NULL,
  `info_source` text DEFAULT NULL,
  `baseline` decimal(15,2) DEFAULT NULL,
  `baseline_date` date DEFAULT NULL,
  `target` decimal(15,2) DEFAULT NULL,
  `target_date` date DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_indicators`
--

INSERT INTO `plans_indicators` (`id`, `org_id`, `plan_id`, `program_id`, `indicator_code`, `indicator_name`, `info_source`, `baseline`, `baseline_date`, `target`, `target_date`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 2, 2, 3, 'IN002', 'This is indi', 'All sources', 0.00, '2024-10-29', 100.00, '2024-11-08', NULL, 14, '2024-11-11 17:39:51', '2024-11-11 18:57:41'),
(2, 2, 2, 3, 'IN003', 'Indicates', 'this is information source', 20.00, '2024-10-30', 50.00, '2024-11-10', 14, NULL, '2024-11-11 18:59:33', '2024-11-11 18:59:33'),
(3, 2, 2, 3, 'IN004', 'Indicates', 'Information Source', 30.00, '2024-11-01', 70.00, '2024-11-08', 14, 14, '2024-11-11 19:01:01', '2024-11-11 19:17:51'),
(5, 2, 2, 3, 'IN005', 'Indicates', 'this infor soruce', 30.00, '2024-08-13', 100.00, '2024-11-15', 14, NULL, '2024-11-11 19:29:07', '2024-11-11 19:29:07');

-- --------------------------------------------------------

--
-- Table structure for table `plans_join_deliverables_indicators`
--

CREATE TABLE `plans_join_deliverables_indicators` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `program_id` int(11) NOT NULL,
  `deliverables_id` int(11) NOT NULL,
  `indicators_id` int(11) NOT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_join_deliverables_indicators`
--

INSERT INTO `plans_join_deliverables_indicators` (`id`, `org_id`, `plan_id`, `program_id`, `deliverables_id`, `indicators_id`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(2, 2, 2, 3, 2, 2, 14, NULL, '2024-11-11 18:59:33', '2024-11-11 18:59:33'),
(5, 0, 0, 0, 1, 3, NULL, 14, '2024-11-11 19:17:51', '2024-11-11 19:17:51'),
(6, 2, 2, 3, 1, 5, 14, NULL, '2024-11-11 19:29:07', '2024-11-11 19:29:07'),
(7, 2, 2, 3, 2, 5, 14, NULL, '2024-11-11 19:29:07', '2024-11-11 19:29:07');

-- --------------------------------------------------------

--
-- Table structure for table `plans_join_kras_kpis`
--

CREATE TABLE `plans_join_kras_kpis` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `kra_id` int(11) NOT NULL,
  `kpi_id` int(11) NOT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_join_kras_kpis`
--

INSERT INTO `plans_join_kras_kpis` (`id`, `org_id`, `plan_id`, `kra_id`, `kpi_id`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(6, 2, 3, 1, 1, NULL, 14, '2024-11-11 21:09:19', '2024-11-11 21:09:19'),
(10, 2, 3, 1, 2, 14, NULL, '2024-11-11 21:12:47', '2024-11-11 21:12:47'),
(11, 2, 3, 2, 2, 14, NULL, '2024-11-11 21:12:47', '2024-11-11 21:12:47');

-- --------------------------------------------------------

--
-- Table structure for table `plans_join_projects_indicators`
--

CREATE TABLE `plans_join_projects_indicators` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `program_id` int(11) NOT NULL,
  `project_id` int(11) NOT NULL,
  `indicators_id` int(11) NOT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_join_projects_indicators`
--

INSERT INTO `plans_join_projects_indicators` (`id`, `org_id`, `plan_id`, `program_id`, `project_id`, `indicators_id`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(3, 2, 2, 3, 3, 2, 14, '2024-11-12 10:38:19', 0, '2024-11-12 10:38:19'),
(4, 2, 2, 3, 3, 5, 14, '2024-11-12 10:38:19', 0, '2024-11-12 10:38:19'),
(5, 2, 2, 3, 2, 1, 14, '2024-11-12 10:48:45', 0, '2024-11-12 10:48:45'),
(6, 2, 2, 3, 2, 5, 14, '2024-11-12 10:48:45', 0, '2024-11-12 10:48:45'),
(7, 2, 2, 3, 1, 2, 14, '2024-11-12 10:55:51', 0, '2024-11-12 10:55:51');

-- --------------------------------------------------------

--
-- Table structure for table `plans_join_workplans_kpis`
--

CREATE TABLE `plans_join_workplans_kpis` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `workplan_id` int(11) NOT NULL,
  `kpi_id` int(11) NOT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_join_workplans_kpis`
--

INSERT INTO `plans_join_workplans_kpis` (`id`, `org_id`, `plan_id`, `workplan_id`, `kpi_id`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 2, 3, 1, 1, 14, NULL, '2024-11-13 09:55:36', '2024-11-13 09:55:36'),
(2, 2, 3, 1, 2, 14, NULL, '2024-11-13 09:55:36', '2024-11-13 09:55:36'),
(3, 2, 3, 2, 1, 14, NULL, '2024-11-18 10:41:54', '2024-11-18 10:41:54'),
(4, 2, 3, 2, 2, 14, NULL, '2024-11-18 10:41:54', '2024-11-18 10:41:54');

-- --------------------------------------------------------

--
-- Table structure for table `plans_kpis`
--

CREATE TABLE `plans_kpis` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `kpi_code` varchar(50) NOT NULL,
  `kpi_name` varchar(255) NOT NULL,
  `target_date` date DEFAULT NULL,
  `kpi_target` varchar(522) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_kpis`
--

INSERT INTO `plans_kpis` (`id`, `org_id`, `plan_id`, `kpi_code`, `kpi_name`, `target_date`, `kpi_target`, `remarks`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 2, 3, 'KPI001', 'KPI Namba Wan', '2024-10-29', 'Samrt and Cool', 'REmarks of KPI', 14, 14, '2024-11-11 21:04:05', '2024-11-11 21:09:18'),
(2, 2, 3, 'KPI002', 'KPI Namba Tu', '2024-11-10', 'Cook KPI', 'REmarks of this', 14, 14, '2024-11-11 21:10:54', '2024-11-11 21:12:47');

-- --------------------------------------------------------

--
-- Table structure for table `plans_kras`
--

CREATE TABLE `plans_kras` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `kra_code` varchar(50) NOT NULL,
  `kra_name` varchar(255) NOT NULL,
  `target_date` date DEFAULT NULL,
  `kra_target` varchar(522) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_kras`
--

INSERT INTO `plans_kras` (`id`, `org_id`, `plan_id`, `kra_code`, `kra_name`, `target_date`, `kra_target`, `remarks`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 2, 3, 'KRA002', 'This KRA', '2024-11-06', 'Cool Target', 'Wonderful', 14, 14, '2024-11-11 20:47:15', '2024-11-11 21:03:04'),
(2, 2, 3, 'KRA0020', 'This KRA 2', '2024-10-29', 'KRA 2 target', 'This is the remarks', 14, 14, '2024-11-11 21:02:01', '2024-11-11 21:03:18');

-- --------------------------------------------------------

--
-- Table structure for table `plans_programs`
--

CREATE TABLE `plans_programs` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `program_code` varchar(50) NOT NULL,
  `program_title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `estimated_cost` decimal(15,2) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_programs`
--

INSERT INTO `plans_programs` (`id`, `org_id`, `plan_id`, `program_code`, `program_title`, `description`, `estimated_cost`, `start_date`, `end_date`, `status`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(2, 2, 2, 'ESPICT002', 'Program Number one', 'Tfshifhids', 300.00, '2024-11-06', '2024-11-22', 'active', NULL, 0, '2024-11-11 16:03:46', '2024-11-11 16:12:16'),
(3, 2, 2, 'ESPAHR002', 'This is the Title', 'This description', 2250.03, '2024-10-28', '2024-11-01', 'active', NULL, 14, '2024-11-11 16:23:35', '2024-11-11 19:31:16'),
(4, 2, 4, 'MTDP001', 'DIP 01', 'Thifisidf', 4000.00, '2024-10-28', '2024-11-03', 'active', 14, 14, '2024-11-12 08:38:32', '2024-11-12 10:00:56'),
(5, 2, 4, 'MTDP002', 'DIP 02', 'TSfosdjo', 4200.00, '2024-11-01', '2024-11-08', 'active', 14, 14, '2024-11-12 08:39:41', '2024-11-12 10:00:59'),
(6, 2, 4, 'MTDP003', 'DIP 03', 'fafosjodbn', 5600.00, '2024-11-04', '2024-11-10', 'active', 14, 14, '2024-11-12 08:40:08', '2024-11-12 10:01:03');

-- --------------------------------------------------------

--
-- Table structure for table `plans_projects`
--

CREATE TABLE `plans_projects` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `program_id` int(11) NOT NULL,
  `country_id` int(11) DEFAULT NULL,
  `province_id` int(11) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `llg_id` int(11) DEFAULT NULL,
  `location_site` varchar(255) DEFAULT NULL,
  `project_code` varchar(50) DEFAULT NULL,
  `responsible_id` int(11) DEFAULT NULL,
  `crop_id` int(11) DEFAULT NULL,
  `project_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `lon` varchar(50) DEFAULT NULL,
  `lat` varchar(50) DEFAULT NULL,
  `date_start` date DEFAULT NULL,
  `date_end` date DEFAULT NULL,
  `budget` decimal(15,2) DEFAULT NULL,
  `actual_cost` decimal(15,2) DEFAULT NULL,
  `project_report` text DEFAULT NULL,
  `project_rating` decimal(3,2) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_projects`
--

INSERT INTO `plans_projects` (`id`, `org_id`, `plan_id`, `program_id`, `country_id`, `province_id`, `district_id`, `llg_id`, `location_site`, `project_code`, `responsible_id`, `crop_id`, `project_name`, `description`, `lon`, `lat`, `date_start`, `date_end`, `budget`, `actual_cost`, `project_report`, `project_rating`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 2, 2, 3, 1, 90, 19, 1, 'Maprik', 'PR001', 10, 45, 'Oil Palm Project', 'Description', '23.55553', '-3.55665', '2024-10-30', '2024-11-16', 3409.50, NULL, NULL, NULL, 14, 14, '2024-11-12 09:02:13', '2024-11-12 13:13:02'),
(2, 2, 2, 3, 1, 90, 19, 2, 'Maprik', 'PR001', 17, 46, 'Coffee Project', 'Description', '0.9999999999', '-0.9999999999', '2024-10-30', '2024-11-16', 3409.50, NULL, NULL, NULL, 14, 14, '2024-11-12 09:14:09', '2024-11-12 13:13:53'),
(3, 2, 2, 3, 1, 90, 19, 1, 'Maprik', 'PR001', 17, 47, 'Rice Project', 'This is the development plan', '143.372469', '-3.573200', '2024-10-29', '2024-11-03', 3409.50, NULL, NULL, NULL, 14, 14, '2024-11-12 10:38:19', '2024-11-12 13:16:19');

-- --------------------------------------------------------

--
-- Table structure for table `plans_project_phases`
--

CREATE TABLE `plans_project_phases` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `program_id` int(11) NOT NULL,
  `project_id` int(11) NOT NULL,
  `phase_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `actual_cost` decimal(15,2) DEFAULT NULL,
  `status_date` date DEFAULT NULL,
  `status` enum('pending','progressing','completed','hold','canceled') DEFAULT 'pending',
  `status_by` int(11) DEFAULT NULL,
  `phase_rating` decimal(3,2) DEFAULT NULL,
  `phase_rating_by` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_project_phases`
--

INSERT INTO `plans_project_phases` (`id`, `org_id`, `plan_id`, `program_id`, `project_id`, `phase_name`, `description`, `actual_cost`, `status_date`, `status`, `status_by`, `phase_rating`, `phase_rating_by`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 2, 2, 3, 2, 'Phase One1', 'This is Phase One', 23.00, '2024-11-12', 'progressing', 14, 1.00, NULL, 14, 14, '2024-11-12 11:24:03', '2024-11-12 11:28:00'),
(2, 2, 2, 3, 2, 'Phase One', 'This is the phase', 25.00, '2024-11-12', 'pending', 14, NULL, NULL, 14, 14, '2024-11-12 11:28:20', '2024-11-12 11:28:28');

-- --------------------------------------------------------

--
-- Table structure for table `plans_workplan`
--

CREATE TABLE `plans_workplan` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `country_id` int(11) DEFAULT NULL,
  `province_id` int(11) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `workplan_code` varchar(50) DEFAULT NULL,
  `workplan_title` varchar(255) NOT NULL,
  `responsible_id` int(11) DEFAULT NULL,
  `crop_id` int(11) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `date_start` date DEFAULT NULL,
  `date_end` date DEFAULT NULL,
  `budget` decimal(15,2) DEFAULT NULL,
  `report_file` varchar(255) DEFAULT NULL,
  `report_by` int(11) DEFAULT NULL,
  `report_at` datetime DEFAULT NULL,
  `status` enum('active','inactive','completed') DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_workplan`
--

INSERT INTO `plans_workplan` (`id`, `org_id`, `plan_id`, `country_id`, `province_id`, `district_id`, `workplan_code`, `workplan_title`, `responsible_id`, `crop_id`, `description`, `date_start`, `date_end`, `budget`, `report_file`, `report_by`, `report_at`, `status`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 2, 3, 1, 90, 19, 'WP002', 'Workplan One', 17, 45, 'This is the description', '2024-10-29', '2024-11-10', 3456.00, NULL, NULL, NULL, '', 14, 14, '2024-11-13 09:55:36', '2024-11-13 09:57:59'),
(2, 2, 3, 1, 90, 19, 'WP005', 'Title of the Workplan', 17, 0, 'this is description of the time', '2024-11-01', '2024-11-08', 2000.00, NULL, NULL, NULL, 'active', 14, NULL, '2024-11-18 10:41:54', '2024-11-18 10:41:54');

-- --------------------------------------------------------

--
-- Table structure for table `plans_workplan_activities`
--

CREATE TABLE `plans_workplan_activities` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `workplan_id` int(11) NOT NULL,
  `activity_code` varchar(50) DEFAULT NULL,
  `activity_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `budget` decimal(15,2) DEFAULT NULL,
  `crop_id` int(11) NOT NULL,
  `actual_cost` decimal(15,2) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `responsible_id` int(11) DEFAULT NULL,
  `report_file` varchar(255) DEFAULT NULL,
  `report_by` int(11) DEFAULT NULL,
  `report_at` datetime DEFAULT NULL,
  `status` enum('pending','progressing','completed','hold','canceled') DEFAULT 'pending',
  `status_date` date DEFAULT NULL,
  `status_by` int(11) DEFAULT NULL,
  `rating` decimal(3,2) DEFAULT NULL,
  `rated_by` int(11) DEFAULT NULL,
  `rated_at` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_workplan_activities`
--

INSERT INTO `plans_workplan_activities` (`id`, `org_id`, `plan_id`, `workplan_id`, `activity_code`, `activity_name`, `description`, `budget`, `crop_id`, `actual_cost`, `start_date`, `end_date`, `responsible_id`, `report_file`, `report_by`, `report_at`, `status`, `status_date`, `status_by`, `rating`, `rated_by`, `rated_at`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 2, 3, 1, 'ACT002', 'Activity One', 'Tjfisdfsidf', 2340.00, 45, NULL, '2024-10-28', '2024-11-10', 17, NULL, NULL, NULL, 'pending', '2024-11-13', 14, NULL, NULL, NULL, 14, 14, '2024-11-13 10:47:18', '2024-11-13 10:48:24');

-- --------------------------------------------------------

--
-- Table structure for table `plans_workplan_activities_infrastructure`
--

CREATE TABLE `plans_workplan_activities_infrastructure` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `workplan_id` int(11) NOT NULL,
  `activity_id` int(11) NOT NULL,
  `responsible_id` int(11) DEFAULT NULL,
  `crop_id` int(11) DEFAULT NULL,
  `item` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `lon` varchar(50) DEFAULT NULL,
  `lat` varchar(50) DEFAULT NULL,
  `unit_of_measurement` varchar(50) DEFAULT NULL,
  `unit` decimal(10,2) DEFAULT NULL,
  `price_per_unit` decimal(15,2) DEFAULT NULL,
  `quantity` decimal(10,2) DEFAULT NULL,
  `infrastructure_date` date DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_workplan_activities_infrastructure`
--

INSERT INTO `plans_workplan_activities_infrastructure` (`id`, `org_id`, `plan_id`, `workplan_id`, `activity_id`, `responsible_id`, `crop_id`, `item`, `description`, `lon`, `lat`, `unit_of_measurement`, `unit`, `price_per_unit`, `quantity`, `infrastructure_date`, `remarks`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 2, 3, 1, 1, 17, 45, 'Tractor', 'This is Tractor One', '143.372469', '-3.573200', 'Tractor', 1.00, 50000.00, 1.00, '2024-10-29', 'dadjaposdj', 14, 14, '2024-11-13 11:56:59', '2024-11-13 12:01:35');

-- --------------------------------------------------------

--
-- Table structure for table `plans_workplan_activities_inputs`
--

CREATE TABLE `plans_workplan_activities_inputs` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `workplan_id` int(11) NOT NULL,
  `activity_id` int(11) NOT NULL,
  `responsible_id` int(11) DEFAULT NULL,
  `crop_id` int(11) DEFAULT NULL,
  `item` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `unit_of_measurement` varchar(50) DEFAULT NULL,
  `unit` decimal(10,2) DEFAULT NULL,
  `price_per_unit` decimal(15,2) DEFAULT NULL,
  `quantity` decimal(10,2) DEFAULT NULL,
  `input_date` date DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_workplan_activities_inputs`
--

INSERT INTO `plans_workplan_activities_inputs` (`id`, `org_id`, `plan_id`, `workplan_id`, `activity_id`, `responsible_id`, `crop_id`, `item`, `description`, `unit_of_measurement`, `unit`, `price_per_unit`, `quantity`, `input_date`, `remarks`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 2, 3, 1, 1, 17, NULL, 'Rice Seedling Distribution', 'This si the seedling destribution data', 'KG', 1.00, 5.40, 45.00, '2024-11-06', 'Cool Rice it is', 14, NULL, '2024-11-13 11:33:39', '2024-11-13 11:35:43'),
(2, 2, 3, 1, 1, 17, NULL, 'Rice Seedling Done', 'Tfhidsf', 'Grams', 1.00, 5.40, 45.00, '2024-11-01', 'fsidfjsdi', 14, 14, '2024-11-13 11:35:29', '2024-11-13 11:37:33');

-- --------------------------------------------------------

--
-- Table structure for table `plans_workplan_activities_training`
--

CREATE TABLE `plans_workplan_activities_training` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `workplan_id` int(11) NOT NULL,
  `activity_id` int(11) NOT NULL,
  `responsible_id` int(11) DEFAULT NULL,
  `crop_id` int(11) DEFAULT NULL,
  `country_id` int(11) DEFAULT NULL,
  `province_id` int(11) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `llg_id` int(11) DEFAULT NULL,
  `location_name` varchar(255) DEFAULT NULL,
  `training_title` varchar(255) NOT NULL,
  `training_type` varchar(100) DEFAULT NULL,
  `lon` varchar(50) DEFAULT NULL,
  `lat` varchar(50) DEFAULT NULL,
  `facilitator` varchar(255) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `actual_cost` decimal(15,2) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_workplan_activities_training`
--

INSERT INTO `plans_workplan_activities_training` (`id`, `org_id`, `plan_id`, `workplan_id`, `activity_id`, `responsible_id`, `crop_id`, `country_id`, `province_id`, `district_id`, `llg_id`, `location_name`, `training_title`, `training_type`, `lon`, `lat`, `facilitator`, `start_date`, `end_date`, `actual_cost`, `remarks`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 2, 3, 1, 1, 17, NULL, 1, 90, 19, 2, 'Dreks', 'TRainifn one', 'Cool', '143.372469', '-3.573200', 'Guitar Smart', '2024-10-29', '2024-11-08', 23.00, 'reamakfdnr', 14, 14, '2024-11-13 12:29:13', '2024-11-13 12:36:18');

-- --------------------------------------------------------

--
-- Table structure for table `plans_workplan_activities_training_participants`
--

CREATE TABLE `plans_workplan_activities_training_participants` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `plan_id` int(11) NOT NULL,
  `workplan_id` int(11) NOT NULL,
  `activity_id` int(11) NOT NULL,
  `training_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `age` int(11) DEFAULT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `farmer_code` varchar(50) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `plans_workplan_activities_training_participants`
--

INSERT INTO `plans_workplan_activities_training_participants` (`id`, `org_id`, `plan_id`, `workplan_id`, `activity_id`, `training_id`, `name`, `age`, `gender`, `farmer_code`, `remarks`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 2, 3, 1, 1, 1, 'Cool Max', 34, 'female', 'F707995', 'This si the tkoef', 14, 14, '2024-11-13 12:40:28', '2024-11-13 13:20:01');

-- --------------------------------------------------------

--
-- Table structure for table `selection`
--

CREATE TABLE `selection` (
  `id` int(11) NOT NULL,
  `box` varchar(20) NOT NULL,
  `value` varchar(200) NOT NULL,
  `item` varchar(200) NOT NULL,
  `hints` text NOT NULL,
  `icons` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `value` varchar(200) NOT NULL,
  `name` varchar(200) NOT NULL,
  `create_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `value`, `name`, `create_at`) VALUES
(1, 'PG', 'country', '2023-03-11 13:50:34');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) UNSIGNED NOT NULL,
  `org_id` int(11) NOT NULL,
  `orgcode` int(11) NOT NULL,
  `fileno` varchar(100) NOT NULL,
  `name` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','supervisor','user','guest') NOT NULL DEFAULT 'user',
  `position` varchar(255) DEFAULT NULL,
  `id_photo` varchar(500) NOT NULL,
  `phone` varchar(200) NOT NULL,
  `email` varchar(500) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 0,
  `created_by` varchar(200) DEFAULT NULL,
  `updated_by` varchar(200) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `org_id`, `orgcode`, `fileno`, `name`, `username`, `password`, `role`, `position`, `id_photo`, `phone`, `email`, `is_active`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(6, 2, 0, 'ESP0024', 'Rc4 Admin', 'admin', '$2y$10$u2WThss1GKD/.cWwQl0Gt.q14X83Gz2jxJcFx1Hge5EZxTLpW6ObC', 'admin', 'DASa', '', '', '', 1, NULL, 'minad', '2023-03-17 03:48:51', '2024-11-01 09:23:39'),
(7, 2, 0, 'ESP0025', 'Photo MAngi', 'foto', '$2y$10$lEV0JqkUUrdmkQ.B27ibB.g77Ai1AkEoj3Y2ZU89IAa1ZaUZ4paTu', 'admin', 'PostCol', '', '', '', 0, NULL, 'minad', '2023-03-20 09:21:32', '2024-11-01 09:24:05'),
(8, 2, 0, 'ESP0026', 'Cool Boy', 'cboy', '$2y$10$9834LWBkfvBXjYKWefjFy.zvwZkWtoiLLGKyHr/pxodMusR1GS6fq', 'user', 'Forllo', '', '', '', 1, NULL, 'minad', '2023-03-28 01:10:41', '2024-11-01 09:24:36'),
(10, 2, 0, 'ESP0027', 'Da Moon', 'dada', '$2y$10$XxfirPWI8RkYNrqbHide7.i7MQ7HPVUQkEXqedDoLi8s1yq1JbK.G', 'supervisor', 'Mones', '', '', '', 1, NULL, 'minad', '2023-03-28 01:15:58', '2024-11-01 09:24:53'),
(11, 2, 0, 'ESP0022', 'dafedw', 'dadw', '$2y$10$QtyktCRSvsjPPleLhITOGe1ZOBzDjZ2rNhfTNqTbc5oYbwOlAmsQa', 'user', 'Cokin', '', '', '', 0, NULL, 'minad', '2023-03-28 01:16:44', '2024-11-01 09:25:07'),
(12, 2, 0, 'ESP0020', 'dasda', 'ssdad', '$2y$10$JPDktb1yW3UQ6T8kkSr0BOGyWhhpSjrLef5h4wLIc.gQr8n5FHYUi', 'user', 'Wadji', '', '', '', 0, NULL, 'minad', '2023-03-28 01:19:52', '2024-11-01 09:25:21'),
(13, 2, 2345, 'ESP0016', 'DadaFo', 'dadafo', '$2y$10$PQ.vKcORr8iGOzRl32qAPupBtr17LYb61nmJIHS8Hz8nlXWAcfr/.', 'guest', 'Hkolo', '', '', '', 1, NULL, NULL, '2023-03-28 01:28:56', '2025-01-04 08:29:11'),
(14, 2, 2345, 'ESP0012', 'Minad', 'minad', '$2y$10$3AUGVe1Sg5jlSCMOb.sG8ewZIzChkyToPrUrMm9QZY1n26lkwmLea', 'admin', 'Flajo', '', '', '', 1, NULL, 'minad', '2023-03-28 01:32:20', '2024-11-01 09:43:04'),
(17, 2, 2345, 'ESP0010', 'Wan Bol', 'wbol', '$2y$10$9dJoD/FVRsHmvBv1R/cDquaRL.DH7AJ.6JK6T2bOysOto/4zNuQq6', 'user', 'Rite ', '', '', '', 1, 'minad', NULL, '2024-11-01 05:24:01', '2024-11-01 09:51:58'),
(19, 2, 2345, 'ESP0009', 'Cook Groups', 'cfrend', '$2y$10$luy/ztyNnqEpMTNGy5lya.TXtLTYVQF66WDBQ6mGW5n1F2aKSxNPy', 'user', 'FFOO', '', '', '', 1, 'minad', NULL, '2024-11-01 05:33:12', '2025-01-04 08:26:57');

-- --------------------------------------------------------

--
-- Table structure for table `workplan`
--

CREATE TABLE `workplan` (
  `workplan_id` int(11) NOT NULL,
  `org_id` int(11) DEFAULT NULL,
  `task_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `assigned_to` int(11) DEFAULT NULL,
  `start_date` date NOT NULL,
  `due_date` date NOT NULL,
  `status` enum('pending','progress','completed','hold','canceled') DEFAULT 'pending',
  `priority` enum('low','medium','high') DEFAULT 'medium',
  `progress_percentage` int(11) DEFAULT 0,
  `dependencies` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `comments` text DEFAULT NULL,
  `created_by` varchar(200) NOT NULL,
  `updated_by` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `workplan`
--

INSERT INTO `workplan` (`workplan_id`, `org_id`, `task_name`, `description`, `assigned_to`, `start_date`, `due_date`, `status`, `priority`, `progress_percentage`, `dependencies`, `created_at`, `updated_at`, `comments`, `created_by`, `updated_by`) VALUES
(1, 2, 'Conduct Training', 'dfsfd', 10, '2024-11-05', '2024-11-15', 'progress', 'high', 40, NULL, '2024-11-01 08:28:25', '2024-11-01 09:39:25', NULL, 'minad', 'minad');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `adx_country`
--
ALTER TABLE `adx_country`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_crops`
--
ALTER TABLE `adx_crops`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_district`
--
ALTER TABLE `adx_district`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_education`
--
ALTER TABLE `adx_education`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_fertilizers`
--
ALTER TABLE `adx_fertilizers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_infections`
--
ALTER TABLE `adx_infections`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_livestock`
--
ALTER TABLE `adx_livestock`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_llg`
--
ALTER TABLE `adx_llg`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_pesticides`
--
ALTER TABLE `adx_pesticides`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_province`
--
ALTER TABLE `adx_province`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_ward`
--
ALTER TABLE `adx_ward`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_blocks`
--
ALTER TABLE `crops_farm_blocks`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_crops_data`
--
ALTER TABLE `crops_farm_crops_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_disease_data`
--
ALTER TABLE `crops_farm_disease_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_fertilizer_data`
--
ALTER TABLE `crops_farm_fertilizer_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_harvest_data`
--
ALTER TABLE `crops_farm_harvest_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_marketing_data`
--
ALTER TABLE `crops_farm_marketing_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_pesticides_data`
--
ALTER TABLE `crops_farm_pesticides_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_tools`
--
ALTER TABLE `crops_farm_tools`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crop_buyers`
--
ALTER TABLE `crop_buyers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `buyer_code` (`buyer_code`);

--
-- Indexes for table `crop_processors`
--
ALTER TABLE `crop_processors`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `dakoii_org`
--
ALTER TABLE `dakoii_org`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `dakoii_users`
--
ALTER TABLE `dakoii_users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `farmers_children`
--
ALTER TABLE `farmers_children`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `farmer_information`
--
ALTER TABLE `farmer_information`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `groupings`
--
ALTER TABLE `groupings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_parent_id` (`parent_id`);

--
-- Indexes for table `livestock_farm_blocks`
--
ALTER TABLE `livestock_farm_blocks`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `livestock_farm_data`
--
ALTER TABLE `livestock_farm_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `livestock_production_data`
--
ALTER TABLE `livestock_production_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `permissions_items`
--
ALTER TABLE `permissions_items`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `permissions_sets`
--
ALTER TABLE `permissions_sets`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `permissions_user_districts`
--
ALTER TABLE `permissions_user_districts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans`
--
ALTER TABLE `plans`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_deliverables`
--
ALTER TABLE `plans_deliverables`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_indicators`
--
ALTER TABLE `plans_indicators`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_join_deliverables_indicators`
--
ALTER TABLE `plans_join_deliverables_indicators`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_join_kras_kpis`
--
ALTER TABLE `plans_join_kras_kpis`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_join_projects_indicators`
--
ALTER TABLE `plans_join_projects_indicators`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_join_workplans_kpis`
--
ALTER TABLE `plans_join_workplans_kpis`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_kpis`
--
ALTER TABLE `plans_kpis`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_kras`
--
ALTER TABLE `plans_kras`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_programs`
--
ALTER TABLE `plans_programs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_projects`
--
ALTER TABLE `plans_projects`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_project_phases`
--
ALTER TABLE `plans_project_phases`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_workplan`
--
ALTER TABLE `plans_workplan`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `workplan_code` (`workplan_code`);

--
-- Indexes for table `plans_workplan_activities`
--
ALTER TABLE `plans_workplan_activities`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `activity_code` (`activity_code`);

--
-- Indexes for table `plans_workplan_activities_infrastructure`
--
ALTER TABLE `plans_workplan_activities_infrastructure`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_workplan_activities_inputs`
--
ALTER TABLE `plans_workplan_activities_inputs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_workplan_activities_training`
--
ALTER TABLE `plans_workplan_activities_training`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `plans_workplan_activities_training_participants`
--
ALTER TABLE `plans_workplan_activities_training_participants`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `selection`
--
ALTER TABLE `selection`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `workplan`
--
ALTER TABLE `workplan`
  ADD PRIMARY KEY (`workplan_id`),
  ADD KEY `idx_assigned_to` (`assigned_to`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `adx_country`
--
ALTER TABLE `adx_country`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `adx_crops`
--
ALTER TABLE `adx_crops`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `adx_district`
--
ALTER TABLE `adx_district`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=41;

--
-- AUTO_INCREMENT for table `adx_education`
--
ALTER TABLE `adx_education`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `adx_fertilizers`
--
ALTER TABLE `adx_fertilizers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `adx_infections`
--
ALTER TABLE `adx_infections`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `adx_livestock`
--
ALTER TABLE `adx_livestock`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `adx_llg`
--
ALTER TABLE `adx_llg`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `adx_pesticides`
--
ALTER TABLE `adx_pesticides`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `adx_province`
--
ALTER TABLE `adx_province`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=106;

--
-- AUTO_INCREMENT for table `adx_ward`
--
ALTER TABLE `adx_ward`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `crops_farm_blocks`
--
ALTER TABLE `crops_farm_blocks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `crops_farm_crops_data`
--
ALTER TABLE `crops_farm_crops_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `crops_farm_disease_data`
--
ALTER TABLE `crops_farm_disease_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `crops_farm_fertilizer_data`
--
ALTER TABLE `crops_farm_fertilizer_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `crops_farm_harvest_data`
--
ALTER TABLE `crops_farm_harvest_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `crops_farm_marketing_data`
--
ALTER TABLE `crops_farm_marketing_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `crops_farm_pesticides_data`
--
ALTER TABLE `crops_farm_pesticides_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `crops_farm_tools`
--
ALTER TABLE `crops_farm_tools`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `crop_buyers`
--
ALTER TABLE `crop_buyers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `crop_processors`
--
ALTER TABLE `crop_processors`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `dakoii_org`
--
ALTER TABLE `dakoii_org`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `dakoii_users`
--
ALTER TABLE `dakoii_users`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `farmers_children`
--
ALTER TABLE `farmers_children`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `farmer_information`
--
ALTER TABLE `farmer_information`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `groupings`
--
ALTER TABLE `groupings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `livestock_farm_blocks`
--
ALTER TABLE `livestock_farm_blocks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `livestock_farm_data`
--
ALTER TABLE `livestock_farm_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `livestock_production_data`
--
ALTER TABLE `livestock_production_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `permissions_items`
--
ALTER TABLE `permissions_items`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `permissions_sets`
--
ALTER TABLE `permissions_sets`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `permissions_user_districts`
--
ALTER TABLE `permissions_user_districts`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `plans`
--
ALTER TABLE `plans`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `plans_deliverables`
--
ALTER TABLE `plans_deliverables`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `plans_indicators`
--
ALTER TABLE `plans_indicators`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `plans_join_deliverables_indicators`
--
ALTER TABLE `plans_join_deliverables_indicators`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `plans_join_kras_kpis`
--
ALTER TABLE `plans_join_kras_kpis`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `plans_join_projects_indicators`
--
ALTER TABLE `plans_join_projects_indicators`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `plans_join_workplans_kpis`
--
ALTER TABLE `plans_join_workplans_kpis`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `plans_kpis`
--
ALTER TABLE `plans_kpis`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `plans_kras`
--
ALTER TABLE `plans_kras`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `plans_programs`
--
ALTER TABLE `plans_programs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `plans_projects`
--
ALTER TABLE `plans_projects`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `plans_project_phases`
--
ALTER TABLE `plans_project_phases`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `plans_workplan`
--
ALTER TABLE `plans_workplan`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `plans_workplan_activities`
--
ALTER TABLE `plans_workplan_activities`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `plans_workplan_activities_infrastructure`
--
ALTER TABLE `plans_workplan_activities_infrastructure`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `plans_workplan_activities_inputs`
--
ALTER TABLE `plans_workplan_activities_inputs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `plans_workplan_activities_training`
--
ALTER TABLE `plans_workplan_activities_training`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `plans_workplan_activities_training_participants`
--
ALTER TABLE `plans_workplan_activities_training_participants`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `selection`
--
ALTER TABLE `selection`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=75;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `workplan`
--
ALTER TABLE `workplan`
  MODIFY `workplan_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `groupings`
--
ALTER TABLE `groupings`
  ADD CONSTRAINT `fk_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `groupings` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
