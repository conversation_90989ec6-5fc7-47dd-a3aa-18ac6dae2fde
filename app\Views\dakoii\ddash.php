<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <!-- Organizations Stats -->
        <div class="col-md-3">
            <div class="card bg-primary text-white shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-0">Organizations</h6>
                            <h2 class="mt-2 mb-0"><?= count($org) ?></h2>
                        </div>
                        <div class="rounded-circle bg-white p-3">
                            <i class="fas fa-building text-primary"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small>
                            Active: <?= count(array_filter($org, fn($o) => $o['is_active'] == 1)) ?>
                            | Paid: <?= count(array_filter($org, fn($o) => $o['license_status'] == 'paid')) ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Provinces Stats -->
        <div class="col-md-3">
            <div class="card bg-success text-white shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-0">Provinces</h6>
                            <h2 class="mt-2 mb-0"><?= $provinces_count ?></h2>
                        </div>
                        <div class="rounded-circle bg-white p-3">
                            <i class="fas fa-map-marker-alt text-success"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small>
                            Districts: <?= $districts_count ?> |
                            LLGs: <?= $llgs_count ?> |
                            Wards: <?= $wards_count ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Users Stats -->
        <div class="col-md-3">
            <div class="card bg-info text-white shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-0">System Users</h6>
                            <h2 class="mt-2 mb-0"><?= count($dusers) ?></h2>
                        </div>
                        <div class="rounded-circle bg-white p-3">
                            <i class="fas fa-users text-info"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small>Active: <?= count(array_filter($dusers, fn($u) => $u['is_active'] == 1)) ?></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Organization Admins Stats -->
        <div class="col-md-3">
            <div class="card bg-warning text-white shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-0">Org Admins</h6>
                            <h2 class="mt-2 mb-0"><?= count($admins) ?></h2>
                        </div>
                        <div class="rounded-circle bg-white p-3">
                            <i class="fas fa-user-shield text-warning"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small>Active: <?= count(array_filter($admins, fn($a) => $a['status'] == 1)) ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Administrative Areas Overview</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>Province</th>
                                    <th>Districts</th>
                                    <th>LLGs</th>
                                    <th>Wards</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($province_stats as $stat): ?>
                                <tr>
                                    <td><?= esc($stat['name']) ?></td>
                                    <td>
                                        <span class="badge badge-info">
                                            <?= number_format($stat['districts']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-success">
                                            <?= number_format($stat['llgs']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">
                                            <?= number_format($stat['wards']) ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot class="bg-light">
                                <tr>
                                    <th>Total</th>
                                    <th><?= number_format($districts_count) ?></th>
                                    <th><?= number_format($llgs_count) ?></th>
                                    <th><?= number_format($wards_count) ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <!-- Map card -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Map</h5>
                </div>
                <div class="card-body">
                    <div id="map" style="height: 500px;"></div>
                </div>
            </div>

            <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.css" />

            <script>
                const map = L.map('map').setView([0, 0], 2);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    maxZoom: 19,
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map);

                let provincesLayer;

                fetch('<?= base_url(JSON_MAP_PROVINCE) ?>')
                    .then(response => response.json())
                    .then(data => {
                        provincesLayer = L.geoJSON(data, {
                            style: {
                                fillColor: '#808080',
                                weight: 2,
                                opacity: 1,
                                color: 'white',
                                fillOpacity: 0.4
                            },
                            onEachFeature: (feature, layer) => {
                                if (feature.properties) {
                                    layer.bindPopup(Object.entries(feature.properties)
                                        .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
                                        .join('<br>'));
                                }
                            }
                        }).addTo(map);

                        map.fitBounds(provincesLayer.getBounds());
                    })
                    .catch(error => console.error('Error loading GeoJSON:', error));
            </script>
        </div>
    </div>

    <div class="row">
        <!-- Recent Organizations -->
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Organizations</h5>
                    <a href="<?= base_url('organizations') ?>" class="btn btn-sm btn-primary">
                        View All
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Code</th>
                                    <th>License</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($org, 0, 5) as $o): ?>
                                <tr>
                                    <td>
                                        <?php if ($o['orglogo']): ?>
                                            <img src="<?= $o['orglogo'] ?>" alt="Logo" class="img-thumbnail mr-2" style="height: 30px;">
                                        <?php endif; ?>
                                        <?= esc($o['name']) ?>
                                    </td>
                                    <td><?= esc($o['orgcode']) ?></td>
                                    <td>
                                        <span class="badge badge-<?= $o['license_status'] == 'paid' ? 'success' : 'warning' ?>">
                                            <?= ucfirst($o['license_status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?= $o['is_active'] ? 'success' : 'danger' ?>">
                                            <?= $o['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('dopen_org/' . $o['orgcode']) ?>" 
                                           class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Users -->
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">System Users</h5>
                    <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#modelId">
                        <i class="fas fa-plus"></i> Add User
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Username</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($dusers as $user): ?>
                                <tr>
                                    <td><?= esc($user['name']) ?></td>
                                    <td><?= esc($user['username']) ?></td>
                                    <td>
                                        <span class="badge badge-info">
                                            <?= ucfirst($user['role']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?= $user['is_active'] ? 'success' : 'danger' ?>">
                                            <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-primary edit-system-user" 
                                                data-id="<?= $user['id'] ?>"
                                                data-name="<?= esc($user['name']) ?>"
                                                data-username="<?= esc($user['username']) ?>"
                                                data-role="<?= esc($user['role']) ?>"
                                                data-active="<?= $user['is_active'] ?>"
                                                data-toggle="modal" 
                                                data-target="#editSystemUserModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Livestock Management</h5>
                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addLivestockModal">
                        <i class="fas fa-plus"></i> Add Livestock
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Icon</th>
                                    <th>Color Code</th>
                                    <th>Remarks</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($livestock as $item): ?>
                                <tr>
                                    <td><?= esc($item['livestock_name']) ?></td>
                                    <td>
                                        <?php if (!empty($item['livestock_icon'])): ?>
                                            <img src="<?= imgcheck($item['livestock_icon']) ?>" alt="Livestock Icon" class="img-thumbnail" style="height: 40px;">
                                        <?php endif; ?>
                                    </td>
                                    <td><?= esc($item['livestock_color_code']) ?></td>
                                    <td><?= esc($item['remarks']) ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary edit-livestock"
                                                data-id="<?= $item['id'] ?>"
                                                data-name="<?= esc($item['livestock_name']) ?>"
                                                data-icon="<?= imgcheck($item['livestock_icon']) ?>"
                                                data-color="<?= esc($item['livestock_color_code']) ?>"
                                                data-remarks="<?= esc($item['remarks']) ?>"
                                                data-toggle="modal" 
                                                data-target="#editLivestockModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Livestock Modal -->
<div class="modal fade" id="addLivestockModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Livestock</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
<form action="<?= base_url('add-livestock') ?>" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label>Name</label>
                        <input type="text" class="form-control" name="livestock_name" required>
                    </div>
                    <div class="form-group">
                        <label>Icon</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" name="livestock_icon" accept="image/*">
                            <label class="custom-file-label">Choose file</label>
                        </div>
                        <small class="form-text text-muted">Recommended size: 64x64px</small>
                    </div>
                    <div class="form-group">
                        <label>Color Code</label>
                        <input type="color" class="form-control" name="livestock_color_code">
                    </div>
                    <div class="form-group">
                        <label>Remarks</label>
                        <textarea class="form-control" name="remarks"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Livestock Modal -->
<div class="modal fade" id="editLivestockModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Livestock</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="<?= base_url('update-livestock') ?>" method="post" enctype="multipart/form-data">
                <input type="hidden" name="id" id="edit_livestock_id">
                <div class="modal-body">
                    <div class="form-group">
                        <label>Name</label>
                        <input type="text" class="form-control" name="livestock_name" id="edit_livestock_name" required>
                    </div>
                    <div class="form-group">
                        <label>Icon</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" name="livestock_icon" accept="image/*">
                            <label class="custom-file-label">Choose file</label>
                        </div>
                        <small class="form-text text-muted">Leave empty to keep current icon</small>
                        <div id="livestock_icon_preview" class="mt-2"></div>
                    </div>
                    <div class="form-group">
                        <label>Color Code</label>
                        <input type="color" class="form-control" name="livestock_color_code" id="edit_livestock_color">
                    </div>
                    <div class="form-group">
                        <label>Remarks</label>
                        <textarea class="form-control" name="remarks" id="edit_livestock_remarks"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Handle edit livestock button clicks
document.querySelectorAll('.edit-livestock').forEach(button => {
    button.addEventListener('click', function() {
        const id = this.dataset.id;
        const name = this.dataset.name;
        const icon = this.dataset.icon;
        const color = this.dataset.color;
        const remarks = this.dataset.remarks;
        
        document.getElementById('edit_livestock_id').value = id;
        document.getElementById('edit_livestock_name').value = name;
        document.getElementById('edit_livestock_color').value = color;
        document.getElementById('edit_livestock_remarks').value = remarks;
        
        // Show current icon preview
        const previewDiv = document.getElementById('livestock_icon_preview');
        if (icon) {
            previewDiv.innerHTML = `<img src="${icon}" alt="Current Icon" class="img-thumbnail" style="height: 40px;">
                                  <small class="d-block">Current icon</small>`;
        } else {
            previewDiv.innerHTML = '<small>No current icon</small>';
        }
    });
});
</script>

<!-- Add User Modal -->
<div class="modal fade" id="modelId" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus"></i> Add System User
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open('dadduser') ?>
            <div class="modal-body">
                <div class="form-group">
                    <label>Name</label>
                    <input type="text" class="form-control" name="name" required>
                </div>
                <div class="form-group">
                    <label>Username</label>
                    <input type="text" class="form-control" name="username" required>
                </div>
                <div class="form-group">
                    <label>Password</label>
                    <input type="password" class="form-control" name="password" required>
                </div>
                <div class="form-group">
                    <label>Role</label>
                    <select class="form-control" name="role" required>
                        <option value="user">User</option>
                        <option value="moderator">Moderator</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1">
                    <label class="custom-control-label" for="is_active">Active Account</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Create User
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit System User Modal -->
<div class="modal fade" id="editSystemUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit"></i> Edit System User
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open('edit-system-user') ?>
            <div class="modal-body">
                <input type="hidden" name="id" id="edit_user_id">

                <div class="form-group">
                    <label>Name</label>
                    <input type="text" class="form-control" name="name" id="edit_user_name" required>
                </div>

                <div class="form-group">
                    <label>Username</label>
                    <input type="text" class="form-control" name="username" id="edit_user_username" required>
                </div>

                <div class="form-group">
                    <label>New Password</label>
                    <input type="password" class="form-control" name="password" placeholder="Leave blank to keep current password">
                    <small class="form-text text-muted">Only fill this if you want to change the password</small>
                </div>

                <div class="form-group">
                    <label>Role</label>
                    <select class="form-control" name="role" id="edit_user_role" required>
                        <option value="user">User</option>
                        <option value="moderator">Moderator</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>

                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="edit_user_active" name="is_active" value="1">
                    <label class="custom-control-label" for="edit_user_active">Active Account</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update User
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Crops Section -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Crops Management</h5>
                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addCropModal">
                    <i class="fas fa-plus"></i> Add Crop
                </button>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th>Name</th>
                                <th>Icon</th>
                                <th>Color Code</th>
                                <th>Remarks</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($crops as $crop): ?>
                            <tr>
                                <td><?= esc($crop['crop_name']) ?></td>
                                <td>
                                    <?php if (!empty($crop['crop_icon'])): ?>
                                        <img src="<?= imgcheck($crop['crop_icon']) ?>" alt="Crop Icon" class="img-thumbnail" style="height: 40px;">
                                    <?php endif; ?>
                                </td>
                                <td><?= esc($crop['crop_color_code']) ?></td>
                                <td><?= esc($crop['remarks']) ?></td>
                                <td>
                                    <button class="btn btn-sm btn-primary edit-crop"
                                            data-id="<?= $crop['id'] ?>"
                                            data-name="<?= esc($crop['crop_name']) ?>"
                                            data-icon="<?= imgcheck($crop['crop_icon']) ?>"
                                            data-color="<?= esc($crop['crop_color_code']) ?>"
                                            data-remarks="<?= esc($crop['remarks']) ?>"
                                            data-toggle="modal" 
                                            data-target="#editCropModal">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
<!-- Add Crop Modal -->
<div class="modal fade" id="addCropModal" tabindex="-1" role="dialog" aria-labelledby="addCropModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCropModalLabel">Add New Crop</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="<?= base_url('add-crop') ?>" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="crop_name">Crop Name</label>
                        <input type="text" class="form-control" id="crop_name" name="crop_name" required>
                    </div>
                    <div class="form-group">
                        <label for="crop_icon">Icon</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="crop_icon" name="crop_icon" accept="image/*">
                            <label class="custom-file-label" for="crop_icon">Choose file</label>
                        </div>
                        <small class="form-text text-muted">Recommended size: 64x64px</small>
                    </div>
                    <div class="form-group">
                        <label for="crop_color_code">Color Code</label>
                        <input type="color" class="form-control" id="crop_color_code" name="crop_color_code">
                    </div>
                    <div class="form-group">
                        <label for="remarks">Remarks</label>
                        <textarea class="form-control" id="remarks" name="remarks"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Crop Modal -->
<div class="modal fade" id="editCropModal" tabindex="-1" role="dialog" aria-labelledby="editCropModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCropModalLabel">Edit Crop</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="<?= base_url('update-crop') ?>" method="post" enctype="multipart/form-data">
                <input type="hidden" name="id" id="edit_crop_id">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="edit_crop_name">Crop Name</label>
                        <input type="text" class="form-control" id="edit_crop_name" name="crop_name" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_crop_icon">Icon</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="edit_crop_icon" name="crop_icon" accept="image/*">
                            <label class="custom-file-label" for="edit_crop_icon">Choose file</label>
                        </div>
                        <small class="form-text text-muted">Leave empty to keep current icon</small>
                        <div id="current_icon_preview" class="mt-2"></div>
                    </div>
                    <div class="form-group">
                        <label for="edit_crop_color_code">Color Code</label>
                        <input type="color" class="form-control" id="edit_crop_color_code" name="crop_color_code">
                    </div>
                    <div class="form-group">
                        <label for="edit_remarks">Remarks</label>
                        <textarea class="form-control" id="edit_remarks" name="remarks"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Pesticides Section -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Pesticides Management</h5>
                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addPesticideModal">
                    <i class="fas fa-plus"></i> Add Pesticide
                </button>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th>Name</th>
                                <th>Icon</th>
                                <th>Color Code</th>
                                <th>Remarks</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pesticides as $pesticide): ?>
                            <tr>
                                <td><?= esc($pesticide['name']) ?></td>
                                <td>
                                    <?php if (!empty($pesticide['icon'])): ?>
                                        <img src="<?= imgcheck($pesticide['icon']) ?>" alt="Pesticide Icon" class="img-thumbnail" style="height: 40px;">
                                    <?php endif; ?>
                                </td>
                                <td><?= esc($pesticide['color_code']) ?></td>
                                <td><?= esc($pesticide['remarks']) ?></td>
                                <td>
                                    <button class="btn btn-sm btn-primary edit-pesticide"
                                            data-id="<?= $pesticide['id'] ?>"
                                            data-name="<?= esc($pesticide['name']) ?>"
                                            data-icon="<?= imgcheck($pesticide['icon']) ?>"
                                            data-color="<?= esc($pesticide['color_code']) ?>"
                                            data-remarks="<?= esc($pesticide['remarks']) ?>"
                                            data-toggle="modal" 
                                            data-target="#editPesticideModal">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Infections Section -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Infections Management</h5>
                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addInfectionModal">
                    <i class="fas fa-plus"></i> Add Infection
                </button>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th>Name</th>
                                <th>Icon</th>
                                <th>Color Code</th>
                                <th>Remarks</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($infections as $infection): ?>
                            <tr>
                                <td><?= esc($infection['name']) ?></td>
                                <td>
                                    <?php if (!empty($infection['icon'])): ?>
                                        <img src="<?= imgcheck($infection['icon']) ?>" alt="Infection Icon" class="img-thumbnail" style="height: 40px;">
                                    <?php endif; ?>
                                </td>
                                <td><?= esc($infection['color_code']) ?></td>
                                <td><?= esc($infection['remarks']) ?></td>
                                <td>
                                    <button class="btn btn-sm btn-primary edit-infection"
                                            data-id="<?= $infection['id'] ?>"
                                            data-name="<?= esc($infection['name']) ?>"
                                            data-icon="<?= imgcheck($infection['icon']) ?>"
                                            data-color="<?= esc($infection['color_code']) ?>"
                                            data-remarks="<?= esc($infection['remarks']) ?>"
                                            data-toggle="modal" 
                                            data-target="#editInfectionModal">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fertilizers Section -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Fertilizers Management</h5>
                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addFertilizerModal">
                    <i class="fas fa-plus"></i> Add Fertilizer
                </button>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th>Name</th>
                                <th>Icon</th>
                                <th>Color Code</th>
                                <th>Remarks</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($fertilizers as $fertilizer): ?>
                            <tr>
                                <td><?= esc($fertilizer['name']) ?></td>
                                <td>
                                    <?php if (!empty($fertilizer['icon'])): ?>
                                        <img src="<?= imgcheck($fertilizer['icon']) ?>" alt="Fertilizer Icon" class="img-thumbnail" style="height: 40px;">
                                    <?php endif; ?>
                                </td>
                                <td><?= esc($fertilizer['color_code']) ?></td>
                                <td><?= esc($fertilizer['remarks']) ?></td>
                                <td>
                                    <button class="btn btn-sm btn-primary edit-fertilizer"
                                            data-id="<?= $fertilizer['id'] ?>"
                                            data-name="<?= esc($fertilizer['name']) ?>"
                                            data-icon="<?= imgcheck($fertilizer['icon']) ?>"
                                            data-color="<?= esc($fertilizer['color_code']) ?>"
                                            data-remarks="<?= esc($fertilizer['remarks']) ?>"
                                            data-toggle="modal" 
                                            data-target="#editFertilizerModal">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Pesticide Modal -->
<div class="modal fade" id="addPesticideModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Pesticide</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="<?= base_url('add-pesticide') ?>" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label>Name</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="form-group">
                        <label>Icon</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" name="icon" accept="image/*">
                            <label class="custom-file-label">Choose file</label>
                        </div>
                        <small class="form-text text-muted">Recommended size: 64x64px</small>
                    </div>
                    <div class="form-group">
                        <label>Color Code</label>
                        <input type="color" class="form-control" name="color_code">
                    </div>
                    <div class="form-group">
                        <label>Remarks</label>
                        <textarea class="form-control" name="remarks"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Pesticide Modal -->
<div class="modal fade" id="editPesticideModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Pesticide</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="<?= base_url('update-pesticide') ?>" method="post" enctype="multipart/form-data">
                <input type="hidden" name="id" id="edit_pesticide_id">
                <div class="modal-body">
                    <div class="form-group">
                        <label>Name</label>
                        <input type="text" class="form-control" name="name" id="edit_pesticide_name" required>
                    </div>
                    <div class="form-group">
                        <label>Icon</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" name="icon" accept="image/*">
                            <label class="custom-file-label">Choose file</label>
                        </div>
                        <small class="form-text text-muted">Leave empty to keep current icon</small>
                        <div id="pesticide_icon_preview" class="mt-2"></div>
                    </div>
                    <div class="form-group">
                        <label>Color Code</label>
                        <input type="color" class="form-control" name="color_code" id="edit_pesticide_color">
                    </div>
                    <div class="form-group">
                        <label>Remarks</label>
                        <textarea class="form-control" name="remarks" id="edit_pesticide_remarks"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Infection Modal -->
<div class="modal fade" id="addInfectionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Infection</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="<?= base_url('add-infection') ?>" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label>Name</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="form-group">
                        <label>Icon</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" name="icon" accept="image/*">
                            <label class="custom-file-label">Choose file</label>
                        </div>
                        <small class="form-text text-muted">Recommended size: 64x64px</small>
                    </div>
                    <div class="form-group">
                        <label>Color Code</label>
                        <input type="color" class="form-control" name="color_code">
                    </div>
                    <div class="form-group">
                        <label>Remarks</label>
                        <textarea class="form-control" name="remarks"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Infection Modal -->
<div class="modal fade" id="editInfectionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Infection</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="<?= base_url('update-infection') ?>" method="post" enctype="multipart/form-data">
                <input type="hidden" name="id" id="edit_infection_id">
                <div class="modal-body">
                    <div class="form-group">
                        <label>Name</label>
                        <input type="text" class="form-control" name="name" id="edit_infection_name" required>
                    </div>
                    <div class="form-group">
                        <label>Icon</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" name="icon" accept="image/*">
                            <label class="custom-file-label">Choose file</label>
                        </div>
                        <small class="form-text text-muted">Leave empty to keep current icon</small>
                        <div id="infection_icon_preview" class="mt-2"></div>
                    </div>
                    <div class="form-group">
                        <label>Color Code</label>
                        <input type="color" class="form-control" name="color_code" id="edit_infection_color">
                    </div>
                    <div class="form-group">
                        <label>Remarks</label>
                        <textarea class="form-control" name="remarks" id="edit_infection_remarks"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Fertilizer Modal -->
<div class="modal fade" id="addFertilizerModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Fertilizer</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="<?= base_url('add-fertilizer') ?>" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label>Name</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="form-group">
                        <label>Icon</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" name="icon" accept="image/*">
                            <label class="custom-file-label">Choose file</label>
                        </div>
                        <small class="form-text text-muted">Recommended size: 64x64px</small>
                    </div>
                    <div class="form-group">
                        <label>Color Code</label>
                        <input type="color" class="form-control" name="color_code">
                    </div>
                    <div class="form-group">
                        <label>Remarks</label>
                        <textarea class="form-control" name="remarks"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Fertilizer Modal -->
<div class="modal fade" id="editFertilizerModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Fertilizer</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="<?= base_url('update-fertilizer') ?>" method="post" enctype="multipart/form-data">
                <input type="hidden" name="id" id="edit_fertilizer_id">
                <div class="modal-body">
                    <div class="form-group">
                        <label>Name</label>
                        <input type="text" class="form-control" name="name" id="edit_fertilizer_name" required>
                    </div>
                    <div class="form-group">
                        <label>Icon</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" name="icon" accept="image/*">
                            <label class="custom-file-label">Choose file</label>
                        </div>
                        <small class="form-text text-muted">Leave empty to keep current icon</small>
                        <div id="fertilizer_icon_preview" class="mt-2"></div>
                    </div>
                    <div class="form-group">
                        <label>Color Code</label>
                        <input type="color" class="form-control" name="color_code" id="edit_fertilizer_color">
                    </div>
                    <div class="form-group">
                        <label>Remarks</label>
                        <textarea class="form-control" name="remarks" id="edit_fertilizer_remarks"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    margin-bottom: 1.5rem;
}
.card-header {
    border-bottom: none;
}
.badge {
    font-size: 85%;
}
.table td, .table th {
    vertical-align: middle;
}
.rounded-circle {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.rounded-circle i {
    font-size: 1.5rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit system user button clicks
    document.querySelectorAll('.edit-system-user').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.dataset.id;
            const name = this.dataset.name;
            const username = this.dataset.username;
            const role = this.dataset.role;
            const active = this.dataset.active;
            
            document.getElementById('edit_user_id').value = id;
            document.getElementById('edit_user_name').value = name;
            document.getElementById('edit_user_username').value = username;
            document.getElementById('edit_user_role').value = role;
            document.getElementById('edit_user_active').checked = active === '1';
        });
    });

    // Update file input label with selected filename
    document.querySelectorAll('.custom-file-input').forEach(input => {
        input.addEventListener('change', function(e) {
            const fileName = this.files[0].name;
            const label = this.nextElementSibling;
            label.textContent = fileName;
        });
    });

    // Update edit modal with current icon preview
    document.querySelectorAll('.edit-crop').forEach(button => {
        button.addEventListener('click', function() {
            // Existing data population
            const id = this.dataset.id;
            const name = this.dataset.name;
            const icon = this.dataset.icon;
            const color = this.dataset.color;
            const remarks = this.dataset.remarks;
            
            document.getElementById('edit_crop_id').value = id;
            document.getElementById('edit_crop_name').value = name;
            document.getElementById('edit_crop_color_code').value = color;
            document.getElementById('edit_remarks').value = remarks;
            
            // Show current icon preview
            const previewDiv = document.getElementById('current_icon_preview');
            if (icon) {
                previewDiv.innerHTML = `<img src="${icon}" alt="Current Icon" class="img-thumbnail" style="height: 40px;">
                                      <small class="d-block">Current icon</small>`;
            } else {
                previewDiv.innerHTML = '<small>No current icon</small>';
            }
        });
    });

    // Pesticide edit handler
    document.querySelectorAll('.edit-pesticide').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.dataset.id;
            const name = this.dataset.name;
            const icon = this.dataset.icon;
            const color = this.dataset.color;
            const remarks = this.dataset.remarks;
            
            document.getElementById('edit_pesticide_id').value = id;
            document.getElementById('edit_pesticide_name').value = name;
            document.getElementById('edit_pesticide_color').value = color;
            document.getElementById('edit_pesticide_remarks').value = remarks;
            
            const previewDiv = document.getElementById('pesticide_icon_preview');
            if (icon) {
                previewDiv.innerHTML = `<img src="${icon}" alt="Current Icon" class="img-thumbnail" style="height: 40px;">
                                      <small class="d-block">Current icon</small>`;
            } else {
                previewDiv.innerHTML = '<small>No current icon</small>';
            }
        });
    });

    // Handle edit infection button clicks
    document.querySelectorAll('.edit-infection').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.dataset.id;
            const name = this.dataset.name;
            const icon = this.dataset.icon;
            const color = this.dataset.color;
            const remarks = this.dataset.remarks;
            
            document.getElementById('edit_infection_id').value = id;
            document.getElementById('edit_infection_name').value = name;
            document.getElementById('edit_infection_color').value = color;
            document.getElementById('edit_infection_remarks').value = remarks;
            
            // Show current icon preview
            const previewDiv = document.getElementById('infection_icon_preview');
            if (icon) {
                previewDiv.innerHTML = `<img src="${icon}" alt="Current Icon" class="img-thumbnail" style="height: 40px;">
                                      <small class="d-block">Current icon</small>`;
            } else {
                previewDiv.innerHTML = '<small>No current icon</small>';
            }
        });
    });

    // Handle edit fertilizer button clicks
    document.querySelectorAll('.edit-fertilizer').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.dataset.id;
            const name = this.dataset.name;
            const icon = this.dataset.icon;
            const color = this.dataset.color;
            const remarks = this.dataset.remarks;
            
            document.getElementById('edit_fertilizer_id').value = id;
            document.getElementById('edit_fertilizer_name').value = name;
            document.getElementById('edit_fertilizer_color').value = color;
            document.getElementById('edit_fertilizer_remarks').value = remarks;
            
            // Show current icon preview
            const previewDiv = document.getElementById('fertilizer_icon_preview');
            if (icon) {
                previewDiv.innerHTML = `<img src="${icon}" alt="Current Icon" class="img-thumbnail" style="height: 40px;">
                                      <small class="d-block">Current icon</small>`;
            } else {
                previewDiv.innerHTML = '<small>No current icon</small>';
            }
        });
    });
});
</script>

<?= $this->endSection() ?>
