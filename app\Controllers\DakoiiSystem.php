<?php

namespace App\Controllers;

use App\Models\dakoiiUsersModel;
use App\Models\orgModel;
use App\Models\selectionModel;

class DakoiiSystem extends BaseController
{
    public $session;
    public $dusersModel;
    public $orgModel;
    public $selectionModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        
        // Initialize models
        $this->dusersModel = new dakoiiUsersModel();
        $this->orgModel = new orgModel();
        $this->selectionModel = new selectionModel();
    }

    /**
     * Display system overview
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "System Management";
        $data['menu'] = "system";
        
        // Get system statistics
        $data['total_organizations'] = $this->orgModel->countAllResults();
        $data['active_organizations'] = $this->orgModel->where('is_active', 1)->countAllResults();
        $data['total_users'] = $this->dusersModel->countAllResults();
        $data['active_users'] = $this->dusersModel->where('is_active', 1)->countAllResults();
        $data['total_selections'] = $this->selectionModel->countAllResults();

        // Get recent activity
        $data['recent_organizations'] = $this->orgModel->orderBy('id', 'DESC')->limit(5)->findAll();
        $data['recent_users'] = $this->dusersModel->orderBy('id', 'DESC')->limit(5)->findAll();

        // Get system information
        $data['system_info'] = $this->getSystemInfo();

        return view('dakoii/dakoii_system_index', $data);
    }

    /**
     * Display system settings
     */
    public function settings()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Check if user has admin role
        if ($this->session->get('dakoii_role') !== 'admin') {
            session()->setFlashdata('error', 'Access denied. Admin privileges required.');
            return redirect()->to('dakoii/system');
        }

        $data['title'] = "System Settings";
        $data['menu'] = "system";
        
        // Get current settings (you can expand this based on your needs)
        $data['settings'] = [
            'system_name' => SYSTEM_NAME ?? 'Dakoii System',
            'system_version' => SYSTEM_VERSION ?? '1.0.0',
            'maintenance_mode' => false, // You can implement this feature
            'max_upload_size' => ini_get('upload_max_filesize'),
            'php_version' => PHP_VERSION,
            'codeigniter_version' => \CodeIgniter\CodeIgniter::CI_VERSION
        ];

        return view('dakoii/dakoii_system_settings', $data);
    }

    /**
     * Update system settings
     */
    public function updateSettings()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Check if user has admin role
        if ($this->session->get('dakoii_role') !== 'admin') {
            session()->setFlashdata('error', 'Access denied. Admin privileges required.');
            return redirect()->to('dakoii/system');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/system/settings');
        }

        // Here you can implement settings update logic
        // For now, we'll just show a success message
        session()->setFlashdata('success', 'System settings updated successfully!');
        
        return redirect()->to('dakoii/system/settings');
    }

    /**
     * Get system information
     */
    private function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'codeigniter_version' => \CodeIgniter\CodeIgniter::CI_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_version' => $this->getDatabaseVersion(),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'timezone' => date_default_timezone_get(),
            'current_time' => date('Y-m-d H:i:s'),
            'disk_space' => $this->getDiskSpace(),
            'extensions' => $this->getRequiredExtensions()
        ];
    }

    /**
     * Get database version
     */
    private function getDatabaseVersion()
    {
        try {
            $db = \Config\Database::connect();
            $query = $db->query("SELECT VERSION() as version");
            $result = $query->getRow();
            return $result->version ?? 'Unknown';
        } catch (\Exception $e) {
            return 'Unable to determine';
        }
    }

    /**
     * Get disk space information
     */
    private function getDiskSpace()
    {
        $bytes = disk_free_space(".");
        $si_prefix = array( 'B', 'KB', 'MB', 'GB', 'TB', 'EB', 'ZB', 'YB' );
        $base = 1024;
        $class = min((int)log($bytes , $base) , count($si_prefix) - 1);
        return sprintf('%1.2f' , $bytes / pow($base,$class)) . ' ' . $si_prefix[$class];
    }

    /**
     * Get required PHP extensions status
     */
    private function getRequiredExtensions()
    {
        $required = [
            'curl' => extension_loaded('curl'),
            'gd' => extension_loaded('gd'),
            'json' => extension_loaded('json'),
            'mbstring' => extension_loaded('mbstring'),
            'mysqli' => extension_loaded('mysqli'),
            'openssl' => extension_loaded('openssl'),
            'pdo' => extension_loaded('pdo'),
            'zip' => extension_loaded('zip')
        ];

        return $required;
    }

    /**
     * Clear system cache (if implemented)
     */
    public function clearCache()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Check if user has admin role
        if ($this->session->get('dakoii_role') !== 'admin') {
            session()->setFlashdata('error', 'Access denied. Admin privileges required.');
            return redirect()->to('dakoii/system');
        }

        try {
            // Clear CodeIgniter cache
            $cache = \Config\Services::cache();
            $cache->clean();

            // You can add more cache clearing logic here
            
            session()->setFlashdata('success', 'System cache cleared successfully!');
        } catch (\Exception $e) {
            session()->setFlashdata('error', 'Failed to clear cache: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/system');
    }

    /**
     * Export system data
     */
    public function exportData()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Check if user has admin role
        if ($this->session->get('dakoii_role') !== 'admin') {
            session()->setFlashdata('error', 'Access denied. Admin privileges required.');
            return redirect()->to('dakoii/system');
        }

        // Here you can implement data export logic
        session()->setFlashdata('info', 'Data export feature will be implemented soon.');
        
        return redirect()->to('dakoii/system');
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }

    /**
     * Get current user information
     */
    private function getCurrentUser()
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        return [
            'id' => $this->session->get('dakoii_user_id'),
            'name' => $this->session->get('dakoii_name'),
            'username' => $this->session->get('dakoii_username'),
            'role' => $this->session->get('dakoii_role'),
            'orgcode' => $this->session->get('dakoii_orgcode')
        ];
    }
}
