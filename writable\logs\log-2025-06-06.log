CRITICAL - 2025-06-06 15:54:52 --> ErrorException: Undefined array key "is_active"
[Method: GET, Route: dakoii/ddash]
in APPPATH\Views\dakoii\ddash.php on line 88.
 1 APPPATH\Views\dakoii\ddash.php(88): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "is_active"', 'C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\ddash.php', 88)
 2 [internal function]: CodeIgniter\View\View->{closure}([...])
 3 APPPATH\Views\dakoii\ddash.php(88): array_filter([...], Object(Closure))
 4 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\agristats\\app\\Views\\dakoii\\ddash.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/ddash', [], true)
 7 APPPATH\Controllers\Dakoii.php(358): view('dakoii/ddash', [...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Dakoii->ddash()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Dakoii))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:19:32 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_data_crops.php"
[Method: GET, Route: dakoii/data/crops]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_data_crops.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_data_crops', [], true)
 3 APPPATH\Controllers\DakoiiData.php(69): view('dakoii/dakoii_data_crops', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiData->crops()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiData))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:19:38 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_data_pesticides.php"
[Method: GET, Route: dakoii/data/pesticides]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_data_pesticides.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_data_pesticides', [], true)
 3 APPPATH\Controllers\DakoiiData.php(323): view('dakoii/dakoii_data_pesticides', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiData->pesticides()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiData))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:19:45 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_locations_index.php"
[Method: GET, Route: dakoii/locations]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_locations_index.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_locations_index', [], true)
 3 APPPATH\Controllers\DakoiiLocations.php(55): view('dakoii/dakoii_locations_index', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiLocations->index()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiLocations))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:19:50 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_system_index.php"
[Method: GET, Route: dakoii/system]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_system_index.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_system_index', [], true)
 3 APPPATH\Controllers\DakoiiSystem.php(55): view('dakoii/dakoii_system_index', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiSystem->index()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiSystem))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:19:58 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_users_index.php"
[Method: GET, Route: dakoii/users]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_users_index.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_users_index', [], true)
 3 APPPATH\Controllers\DakoiiUsers.php(38): view('dakoii/dakoii_users_index', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiUsers->index()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiUsers))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:21:08 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_organizations_edit.php"
[Method: GET, Route: dakoii/organizations/edit/84261]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_organizations_edit.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_organizations_edit', [], true)
 3 APPPATH\Controllers\DakoiiOrganizations.php(204): view('dakoii/dakoii_organizations_edit', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiOrganizations->edit('84261')
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiOrganizations))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:21:19 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_organizations_create.php"
[Method: GET, Route: dakoii/organizations/create]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_organizations_create.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_organizations_create', [], true)
 3 APPPATH\Controllers\DakoiiOrganizations.php(67): view('dakoii/dakoii_organizations_create', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiOrganizations->create()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiOrganizations))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:21:26 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_organizations_create.php"
[Method: GET, Route: dakoii/organizations/create]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_organizations_create.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_organizations_create', [], true)
 3 APPPATH\Controllers\DakoiiOrganizations.php(67): view('dakoii/dakoii_organizations_create', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiOrganizations->create()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiOrganizations))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:25:31 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_users_create.php"
[Method: GET, Route: dakoii/users/create]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_users_create.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_users_create', [], true)
 3 APPPATH\Controllers\DakoiiUsers.php(56): view('dakoii/dakoii_users_create', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiUsers->create()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiUsers))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:27:36 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_locations_index.php"
[Method: GET, Route: dakoii/locations]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_locations_index.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_locations_index', [], true)
 3 APPPATH\Controllers\DakoiiLocations.php(55): view('dakoii/dakoii_locations_index', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiLocations->index()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiLocations))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:37:49 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_data_crops.php"
[Method: GET, Route: dakoii/data/crops]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_data_crops.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_data_crops', [], true)
 3 APPPATH\Controllers\DakoiiData.php(69): view('dakoii/dakoii_data_crops', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiData->crops()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiData))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:47:11 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_data_fertilizers.php"
[Method: GET, Route: dakoii/data/fertilizers]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_data_fertilizers.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_data_fertilizers', [], true)
 3 APPPATH\Controllers\DakoiiData.php(196): view('dakoii/dakoii_data_fertilizers', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiData->fertilizers()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiData))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:48:14 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_data_pesticides.php"
[Method: GET, Route: dakoii/data/pesticides]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_data_pesticides.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_data_pesticides', [], true)
 3 APPPATH\Controllers\DakoiiData.php(323): view('dakoii/dakoii_data_pesticides', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiData->pesticides()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiData))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:52:26 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_data_infections.php"
[Method: GET, Route: dakoii/data/infections]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_data_infections.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_data_infections', [], true)
 3 APPPATH\Controllers\DakoiiData.php(409): view('dakoii/dakoii_data_infections', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiData->infections()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiData))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 16:52:56 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_data_livestock.php"
[Method: GET, Route: dakoii/data/livestock]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_data_livestock.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_data_livestock', [], true)
 3 APPPATH\Controllers\DakoiiData.php(536): view('dakoii/dakoii_data_livestock', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiData->livestock()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiData))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 17:17:14 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_locations_provinces.php"
[Method: GET, Route: dakoii/locations/provinces]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_locations_provinces.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_locations_provinces', [], true)
 3 APPPATH\Controllers\DakoiiLocations.php(81): view('dakoii/dakoii_locations_provinces', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiLocations->provinces()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiLocations))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 17:17:33 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "dakoii/dakoii_locations_districts.php"
[Method: GET, Route: dakoii/locations/districts/90]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('dakoii/dakoii_locations_districts.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('dakoii/dakoii_locations_districts', [], true)
 3 APPPATH\Controllers\DakoiiLocations.php(112): view('dakoii/dakoii_locations_districts', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\DakoiiLocations->districts('90')
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\DakoiiLocations))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 07:17:57 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: dakoii/data/crops/update/1]
in SYSTEMPATH\Security\Security.php on line 262.
 1 SYSTEMPATH\Security\Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH\Filters\CSRF.php(52): CodeIgniter\Security\Security->verify(Object(CodeIgniter\HTTP\IncomingRequest))
 3 SYSTEMPATH\Filters\Filters.php(241): CodeIgniter\Filters\CSRF->before(Object(CodeIgniter\HTTP\IncomingRequest), null)
 4 SYSTEMPATH\Filters\Filters.php(221): CodeIgniter\Filters\Filters->runBefore([...])
 5 SYSTEMPATH\CodeIgniter.php(479): CodeIgniter\Filters\Filters->run('dakoii/data/crops/update/1', 'before')
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-06 07:20:56 --> CodeIgniter\Security\Exceptions\SecurityException: The action you requested is not allowed.
[Method: POST, Route: dakoii/data/fertilizers/update/1]
in SYSTEMPATH\Security\Security.php on line 262.
 1 SYSTEMPATH\Security\Security.php(262): CodeIgniter\Security\Exceptions\SecurityException::forDisallowedAction()
 2 SYSTEMPATH\Filters\CSRF.php(52): CodeIgniter\Security\Security->verify(Object(CodeIgniter\HTTP\IncomingRequest))
 3 SYSTEMPATH\Filters\Filters.php(241): CodeIgniter\Filters\CSRF->before(Object(CodeIgniter\HTTP\IncomingRequest), null)
 4 SYSTEMPATH\Filters\Filters.php(221): CodeIgniter\Filters\Filters->runBefore([...])
 5 SYSTEMPATH\CodeIgniter.php(479): CodeIgniter\Filters\Filters->run('dakoii/data/fertilizers/update/1', 'before')
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(63): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
