<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/data') ?>">Data Management</a></li>
        <li class="breadcrumb-item active">Infections</li>
    </ol>
</nav>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Infections Management</h2>
        <p class="text-muted mb-0">Manage plant diseases and treatment protocols for crop health monitoring</p>
    </div>
    <div class="btn-group">
        <a href="<?= base_url('dakoii/data') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Data
        </a>
        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#addInfectionModal">
            <i class="fas fa-plus"></i> Add Infection
        </button>
    </div>
</div>

<!-- Infections Statistics -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Total Infections</h6>
                        <h3 class="mb-0"><?= count($infections) ?></h3>
                        <small>Plant diseases and infections in the system</small>
                    </div>
                    <i class="fas fa-virus fa-3x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Infections Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list"></i> Infections List
            </h5>
            <input type="text" class="form-control form-control-sm" id="searchInput" 
                   placeholder="Search infections..." style="width: 250px;">
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="infectionsTable">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>Icon</th>
                        <th>Infection Name</th>
                        <th>Color Code</th>
                        <th>Remarks</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($infections)): ?>
                        <?php $i = 1; foreach ($infections as $infection): ?>
                        <tr>
                            <td><?= $i++ ?></td>
                            <td>
                                <?php if (!empty($infection['icon'])): ?>
                                    <img src="<?= base_url($infection['icon']) ?>" alt="Infection Icon" 
                                         class="rounded" style="width: 30px; height: 30px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-secondary rounded d-flex align-items-center justify-content-center" 
                                         style="width: 30px; height: 30px;">
                                        <i class="fas fa-virus text-white"></i>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="fw-medium"><?= esc($infection['name']) ?></div>
                                <small class="text-muted">ID: <?= $infection['id'] ?></small>
                            </td>
                            <td>
                                <?php if (!empty($infection['color_code'])): ?>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded me-2" 
                                             style="width: 20px; height: 20px; background-color: <?= esc($infection['color_code']) ?>; border: 1px solid #dee2e6;"></div>
                                        <code><?= esc($infection['color_code']) ?></code>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">No color</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if (!empty($infection['remarks'])): ?>
                                    <span title="<?= esc($infection['remarks']) ?>">
                                        <?= strlen($infection['remarks']) > 50 ? esc(substr($infection['remarks'], 0, 50)) . '...' : esc($infection['remarks']) ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">No remarks</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary edit-infection" 
                                            data-id="<?= $infection['id'] ?>"
                                            data-name="<?= esc($infection['name']) ?>"
                                            data-color="<?= esc($infection['color_code']) ?>"
                                            data-remarks="<?= esc($infection['remarks']) ?>"
                                            data-bs-toggle="modal" 
                                            data-bs-target="#editInfectionModal"
                                            title="Edit Infection">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <i class="fas fa-virus fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No infections found</p>
                                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#addInfectionModal">
                                    <i class="fas fa-plus"></i> Add First Infection
                                </button>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <small class="text-muted">
                Showing <span id="showingCount"><?= count($infections) ?></span> of <?= count($infections) ?> infections
            </small>
        </div>
    </div>
</div>

<!-- Add Infection Modal -->
<div class="modal fade" id="addInfectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> Add New Infection
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open_multipart('dakoii/data/infections/store') ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="name" class="form-label">Infection Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>

                <div class="mb-3">
                    <label for="icon" class="form-label">Infection Icon</label>
                    <input type="file" class="form-control" id="icon" name="icon" accept="image/*">
                    <div class="form-text">Upload an icon for this infection (optional)</div>
                </div>

                <div class="mb-3">
                    <label for="color_code" class="form-label">Color Code</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="color_code" name="color_code" value="#dc3545">
                        <input type="text" class="form-control" id="color_text" placeholder="#dc3545" readonly>
                    </div>
                    <div class="form-text">Choose a color to represent this infection</div>
                </div>

                <div class="mb-3">
                    <label for="remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Additional notes about this infection..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-save"></i> Add Infection
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Infection Modal -->
<div class="modal fade" id="editInfectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> Edit Infection
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editInfectionForm" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Infection Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_icon" class="form-label">Infection Icon</label>
                        <input type="file" class="form-control" id="edit_icon" name="icon" accept="image/*">
                        <div class="form-text">Upload a new icon to replace the current one (optional)</div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_color_code" class="form-label">Color Code</label>
                        <div class="input-group">
                            <input type="color" class="form-control form-control-color" id="edit_color_code" name="color_code">
                            <input type="text" class="form-control" id="edit_color_text" readonly>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="edit_remarks" name="remarks" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-save"></i> Update Infection
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.opacity-75 {
    opacity: 0.75;
}

.table td {
    vertical-align: middle;
}

.form-control-color {
    width: 3rem;
    height: calc(2.25rem + 2px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const table = document.getElementById('infectionsTable');
    const tbody = table.querySelector('tbody');
    const showingCount = document.getElementById('showingCount');

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = tbody.querySelectorAll('tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        showingCount.textContent = visibleCount;
    });

    // Color picker sync for add modal
    const colorPicker = document.getElementById('color_code');
    const colorText = document.getElementById('color_text');
    
    colorPicker.addEventListener('input', function() {
        colorText.value = this.value;
    });

    // Color picker sync for edit modal
    const editColorPicker = document.getElementById('edit_color_code');
    const editColorText = document.getElementById('edit_color_text');
    
    editColorPicker.addEventListener('input', function() {
        editColorText.value = this.value;
    });

    // Handle edit infection button clicks
    document.querySelectorAll('.edit-infection').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.dataset.id;
            const name = this.dataset.name;
            const color = this.dataset.color;
            const remarks = this.dataset.remarks;
            
            document.getElementById('edit_name').value = name;
            document.getElementById('edit_color_code').value = color || '#dc3545';
            document.getElementById('edit_color_text').value = color || '#dc3545';
            document.getElementById('edit_remarks').value = remarks;
            
            document.getElementById('editInfectionForm').action = '<?= base_url('dakoii/data/infections/update/') ?>' + id;
        });
    });

    // Initialize color text on page load
    colorText.value = colorPicker.value;
});
</script>

<?= $this->endSection() ?>
