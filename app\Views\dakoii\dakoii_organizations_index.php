<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Organizations Management</h2>
        <p class="text-muted mb-0">Manage all registered organizations in the system</p>
    </div>
    <a href="<?= base_url('dakoii/organizations/create') ?>" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add Organization
    </a>
</div>

<!-- Organizations Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Total Organizations</h6>
                        <h3 class="mb-0"><?= count($organizations) ?></h3>
                    </div>
                    <i class="fas fa-building fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Active</h6>
                        <h3 class="mb-0"><?= count(array_filter($organizations, fn($o) => $o['is_active'] == 1)) ?></h3>
                    </div>
                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Paid License</h6>
                        <h3 class="mb-0"><?= count(array_filter($organizations, fn($o) => $o['license_status'] == 'paid')) ?></h3>
                    </div>
                    <i class="fas fa-crown fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Trial License</h6>
                        <h3 class="mb-0"><?= count(array_filter($organizations, fn($o) => $o['license_status'] == 'trial')) ?></h3>
                    </div>
                    <i class="fas fa-clock fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Organizations Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list"></i> Organizations List
            </h5>
            <div class="d-flex gap-2">
                <input type="text" class="form-control form-control-sm" id="searchInput" 
                       placeholder="Search organizations..." style="width: 250px;">
                <select class="form-select form-select-sm" id="statusFilter" style="width: 150px;">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
                <select class="form-select form-select-sm" id="licenseFilter" style="width: 150px;">
                    <option value="">All Licenses</option>
                    <option value="paid">Paid</option>
                    <option value="trial">Trial</option>
                </select>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="organizationsTable">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>Organization</th>
                        <th>Code</th>
                        <th>Description</th>
                        <th>Location Lock</th>
                        <th>License</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $i = 1; foreach ($organizations as $org): ?>
                    <tr data-status="<?= $org['is_active'] ? 'active' : 'inactive' ?>" 
                        data-license="<?= $org['license_status'] ?>">
                        <td><?= $i++ ?></td>
                        <td>
                            <div class="d-flex align-items-center">
                                <?php if (!empty($org['orglogo'])): ?>
                                    <img src="<?= imgcheck($org['orglogo']) ?>" alt="Logo" 
                                         class="rounded me-2" style="height: 40px; width: 40px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-secondary rounded me-2 d-flex align-items-center justify-content-center" 
                                         style="height: 40px; width: 40px;">
                                        <i class="fas fa-building text-white"></i>
                                    </div>
                                <?php endif; ?>
                                <div>
                                    <div class="fw-medium"><?= esc($org['name']) ?></div>
                                    <small class="text-muted">ID: <?= $org['id'] ?></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <code class="bg-light px-2 py-1 rounded"><?= esc($org['orgcode']) ?></code>
                        </td>
                        <td>
                            <div style="max-width: 200px;">
                                <?php if (strlen($org['description']) > 50): ?>
                                    <span title="<?= esc($org['description']) ?>">
                                        <?= esc(substr($org['description'], 0, 50)) ?>...
                                    </span>
                                <?php else: ?>
                                    <?= esc($org['description']) ?>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <?php if ($org['is_locationlocked']): ?>
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-lock"></i> Locked
                                </span>
                                <?php if ($org['addlockcountry'] || $org['addlockprov']): ?>
                                    <br><small class="text-muted">
                                        <?= $org['addlockcountry'] ? 'Country: ' . $org['addlockcountry'] : '' ?>
                                        <?= $org['addlockprov'] ? ' | Province: ' . $org['addlockprov'] : '' ?>
                                    </small>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="badge bg-success">
                                    <i class="fas fa-unlock"></i> Unlocked
                                </span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-<?= $org['license_status'] == 'paid' ? 'success' : 'warning' ?>">
                                <i class="fas fa-<?= $org['license_status'] == 'paid' ? 'crown' : 'clock' ?>"></i>
                                <?= ucfirst($org['license_status']) ?>
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-<?= $org['is_active'] ? 'success' : 'danger' ?>">
                                <i class="fas fa-<?= $org['is_active'] ? 'check' : 'times' ?>"></i>
                                <?= $org['is_active'] ? 'Active' : 'Inactive' ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?= base_url('dakoii/organizations/show/' . $org['orgcode']) ?>" 
                                   class="btn btn-sm btn-outline-primary" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?= base_url('dakoii/organizations/edit/' . $org['orgcode']) ?>" 
                                   class="btn btn-sm btn-outline-secondary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <small class="text-muted">
                Showing <span id="showingCount"><?= count($organizations) ?></span> of <?= count($organizations) ?> organizations
            </small>
            <div>
                <small class="text-muted">Total: <?= count($organizations) ?> organizations</small>
            </div>
        </div>
    </div>
</div>

<style>
.opacity-75 {
    opacity: 0.75;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 0.375rem !important;
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const licenseFilter = document.getElementById('licenseFilter');
    const table = document.getElementById('organizationsTable');
    const tbody = table.querySelector('tbody');
    const showingCount = document.getElementById('showingCount');

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;
        const licenseValue = licenseFilter.value;
        const rows = tbody.querySelectorAll('tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const status = row.dataset.status;
            const license = row.dataset.license;

            const matchesSearch = text.includes(searchTerm);
            const matchesStatus = !statusValue || status === statusValue;
            const matchesLicense = !licenseValue || license === licenseValue;

            if (matchesSearch && matchesStatus && matchesLicense) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        showingCount.textContent = visibleCount;
    }

    searchInput.addEventListener('input', filterTable);
    statusFilter.addEventListener('change', filterTable);
    licenseFilter.addEventListener('change', filterTable);

    // Add tooltips to truncated descriptions
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<?= $this->endSection() ?>
