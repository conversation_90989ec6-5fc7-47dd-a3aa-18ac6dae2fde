-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 06, 2025 at 03:56 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `agristats_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `adx_country`
--

CREATE TABLE `adx_country` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(2) NOT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_country`
--

INSERT INTO `adx_country` (`id`, `name`, `code`, `created_at`) VALUES
(1, 'Papua New Guinea', 'PG', '2023-03-11 10:10:42'),
(2, 'Australia', 'AU', '2023-03-11 10:10:42');

-- --------------------------------------------------------

--
-- Table structure for table `adx_crops`
--

CREATE TABLE `adx_crops` (
  `id` int(11) NOT NULL,
  `crop_name` varchar(255) NOT NULL,
  `crop_icon` varchar(255) NOT NULL,
  `crop_color_code` varchar(7) NOT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_by` int(11) NOT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_crops`
--

INSERT INTO `adx_crops` (`id`, `crop_name`, `crop_icon`, `crop_color_code`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(1, 'Cocoa', 'public/uploads/icons/1732157338_332687c4e9f9e3463a6d.png', '#8a630f', 'This si cocoa', 0, NULL, 0, '2024-11-21 12:48:58'),
(4, 'Coffee', 'public/uploads/icons/1732157982_1a69de9622b3904124dd.png', '#f72b2b', 'Cofffee', 2, '2024-11-21 12:59:42', 2, '2024-11-21 13:01:30'),
(5, 'Rubber', 'public/uploads/icons/1732740553_ab42770fe27bb159e301.png', '#331414', 'rubber crop', 2, '2024-11-28 06:49:13', 0, '2024-11-28 06:49:13');

-- --------------------------------------------------------

--
-- Table structure for table `adx_district`
--

CREATE TABLE `adx_district` (
  `id` int(11) NOT NULL,
  `districtcode` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `json_id` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_district`
--

INSERT INTO `adx_district` (`id`, `districtcode`, `name`, `country_id`, `province_id`, `json_id`) VALUES
(1, '', 'Abau', 1, 86, ''),
(2, '', 'Goilala', 1, 86, ''),
(3, '', 'Kairuku-Hiri', 1, 86, ''),
(4, '', 'Rigo', 1, 86, ''),
(5, '', 'Chimbu', 1, 87, ''),
(6, '', 'Gumine', 1, 87, ''),
(7, '', 'Kerowagi', 1, 87, ''),
(8, '', 'Kundiawa-Gembogl', 1, 87, ''),
(9, '', 'Sinasina-Yonggomugl', 1, 87, ''),
(10, '', 'Goroka', 1, 88, ''),
(11, '', 'Kainantu', 1, 88, ''),
(12, '', 'Lufa', 1, 88, ''),
(13, '', 'Obura-Wonenara', 1, 88, ''),
(14, '', 'Okapa', 1, 88, ''),
(15, '', 'Gazelle', 1, 89, ''),
(16, '', 'Kokopo', 1, 89, ''),
(17, '', 'Pomio', 1, 89, ''),
(18, '', 'Rabaul', 1, 89, ''),
(19, '1401', 'Ambunti-Dreikikir District', 1, 90, 'OCNPNG00301401'),
(20, '1402', 'Angoram District', 1, 90, 'OCNPNG00301402'),
(21, '1403', 'Maprik District', 1, 90, ''),
(22, '1404', 'Wewak District', 1, 90, 'OCNPNG00301404'),
(24, '', 'Kompiam-Ambum', 1, 91, ''),
(25, '', 'Laiagam-Porgera', 1, 91, ''),
(26, '', 'Wabag', 1, 91, ''),
(27, '', 'Kerema', 1, 92, ''),
(28, '', 'Kikori', 1, 92, ''),
(29, '', 'Kopiago', 1, 92, ''),
(30, '', 'Lake Murray', 1, 92, ''),
(31, '', 'Hela', 1, 93, ''),
(32, '', 'Komo-Margarima', 1, 93, ''),
(33, '', 'Koroba-Lake Kopiago', 1, 93, ''),
(34, '', 'Tari-Pori', 1, 93, ''),
(35, '', 'Anglimp-South Waghi', 1, 94, ''),
(36, '', 'Banz', 1, 94, ''),
(37, '', 'Jimi', 1, 94, ''),
(38, '', 'North Waghi', 1, 94, ''),
(39, '1406', 'Wosera Gawi District', 1, 90, ''),
(40, '1405', 'Yangoru-Saussia District', 1, 90, '');

-- --------------------------------------------------------

--
-- Table structure for table `adx_education`
--

CREATE TABLE `adx_education` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `color_code` varchar(20) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_education`
--

INSERT INTO `adx_education` (`id`, `name`, `icon`, `color_code`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(1, 'Gr. 1', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(2, 'Gr. 2', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(3, 'Gr. 3', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(4, 'Gr. 4', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(5, 'Gr. 5', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(6, 'Gr. 6', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(7, 'Gr. 7', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(8, 'Gr. 8', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(9, 'Gr. 9', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(10, 'Gr. 10', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(11, 'Gr. 11', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(12, 'Gr. 12', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(13, 'Vocational / TVET', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(14, 'College', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(15, 'University', NULL, NULL, '', NULL, '2024-11-20 11:39:04', NULL, '2024-11-20 11:39:04'),
(16, 'No Formal Edu.', NULL, NULL, NULL, NULL, '2025-01-22 06:45:15', NULL, '2025-01-22 06:46:39');

-- --------------------------------------------------------

--
-- Table structure for table `adx_fertilizers`
--

CREATE TABLE `adx_fertilizers` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `color_code` varchar(20) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_fertilizers`
--

INSERT INTO `adx_fertilizers` (`id`, `name`, `icon`, `color_code`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(1, 'Nitrogen-Based Fertilizers', NULL, NULL, 'Examples: Urea, ammonium nitrate, and ammonium sulfate. Purpose: Boost plant growth, especially for leafy and green parts, as nitrogen is essential for protein and chlorophyll production.', NULL, '2024-11-20 11:37:26', NULL, '2024-11-20 11:37:26'),
(2, 'Phosphorus-Based Fertilizers', NULL, NULL, 'Examples: Superphosphate, monoammonium phosphate (MAP), and diammonium phosphate (DAP). Purpose: Promote root development, flowering, and fruiting, as phosphorus is essential for energy transfer and root growth.', NULL, '2024-11-20 11:37:26', NULL, '2024-11-20 11:37:26'),
(3, 'Potassium-Based Fertilizers', NULL, NULL, 'Examples: Potassium chloride (muriate of potash), potassium sulfate. Purpose: Enhance disease resistance, improve drought tolerance, and strengthen plant cell walls.', NULL, '2024-11-20 11:37:26', NULL, '2024-11-20 11:37:26'),
(4, 'NPK Blends (Complete Fertilizers)', NULL, NULL, 'Examples: 10-10-10 or 20-20-20 NPK formulations. Purpose: Provide a balanced mix of nitrogen, phosphorus, and potassium to support overall plant health and growth.', NULL, '2024-11-20 11:37:26', NULL, '2024-11-20 11:37:26'),
(5, 'Micronutrient Fertilizers', NULL, NULL, 'Examples: Zinc sulfate, iron chelates, copper sulfate. Purpose: Supply essential trace elements (e.g., iron, zinc, manganese) necessary for enzyme function and chlorophyll formation.', NULL, '2024-11-20 11:37:26', NULL, '2024-11-20 11:37:26'),
(6, 'Bio Fertilizer', '', '#06b15c', 'Using Bio Fertilizers like compost etc...', 2, '2024-12-02 14:47:52', 2, '2024-12-02 14:52:35');

-- --------------------------------------------------------

--
-- Table structure for table `adx_infections`
--

CREATE TABLE `adx_infections` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `color_code` varchar(20) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_infections`
--

INSERT INTO `adx_infections` (`id`, `name`, `icon`, `color_code`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(1, 'Insect Pests', NULL, NULL, 'Common pests include aphids, caterpillars, whiteflies, weevils, beetles, and grasshoppers, which cause damage by feeding on plants or transmitting diseases.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(2, 'Nematodes (Parasitic Worms)', NULL, NULL, 'Types include root-knot nematodes, cyst nematodes, and lesion nematodes, which attack roots, reducing nutrient uptake and causing stunted growth.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(3, 'Fungal Diseases', NULL, NULL, 'Examples include powdery mildew, downy mildew, rust, blight, fusarium wilt, and anthracnose, which affect leaves, stems, and fruits, often reducing photosynthesis and yield.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(4, 'Bacterial Diseases', NULL, NULL, 'Includes bacterial wilt, bacterial blight, fire blight, and soft rot, causing wilting, decay, and spots on leaves and stems.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(5, 'Viral Diseases', NULL, NULL, 'Notable viruses include mosaic viruses, tomato yellow leaf curl virus, banana bunchy top virus, and papaya ringspot virus, causing leaf mottling, stunted growth, and fruit deformities.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(6, 'Mites and Other Small Arthropods', NULL, NULL, 'Includes spider mites, broad mites, and thrips, which damage leaves and fruits, sometimes transmitting plant viruses.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(7, 'Rodents and Mammalian Pests', NULL, NULL, 'Rodents like rats and mice, as well as wild boars and deer, damage crops by consuming or uprooting plants.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02'),
(8, 'Bird Pests', NULL, NULL, 'Birds such as sparrows, crows, pigeons, and parakeets consume seeds and fruits, damaging crop yields.', NULL, '2024-11-20 11:44:02', NULL, '2024-11-20 11:44:02');

-- --------------------------------------------------------

--
-- Table structure for table `adx_livestock`
--

CREATE TABLE `adx_livestock` (
  `id` int(11) NOT NULL,
  `livestock_name` varchar(255) NOT NULL,
  `livestock_icon` varchar(255) DEFAULT NULL,
  `livestock_color_code` varchar(50) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_livestock`
--

INSERT INTO `adx_livestock` (`id`, `livestock_name`, `livestock_icon`, `livestock_color_code`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`) VALUES
(1, 'Cow', 'public/uploads/icons/1735851305_75de0f3630d9c15cd7f2.png', '#7c7979', 'The cow', 2, '2025-01-03 06:52:00', 2, '2025-01-03 06:55:05', 1),
(2, 'Pig', 'public/uploads/icons/1735851990_bd68b47267b671ceee7b.png', '#3c2525', '', 2, '2025-01-03 06:52:45', 2, '2025-01-03 07:06:30', 1),
(3, 'Goat', 'public/uploads/icons/1735852002_49a1397146e9004cbcc2.png', '#8289b0', '', 2, '2025-01-03 06:53:10', 2, '2025-01-03 07:06:42', 1);

-- --------------------------------------------------------

--
-- Table structure for table `adx_llg`
--

CREATE TABLE `adx_llg` (
  `id` int(11) NOT NULL,
  `llgcode` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `json_id` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_llg`
--

INSERT INTO `adx_llg` (`id`, `llgcode`, `name`, `country_id`, `province_id`, `district_id`, `json_id`) VALUES
(1, 'AB01', 'Ambunti LLG', 1, 90, 19, 'OCNPNG0030140101'),
(2, 'AB02', 'Dreikikier District', 1, 90, 19, 'OCNPNG0030140102'),
(3, 'WWK02', 'Wewak Island LLG', 1, 90, 22, 'OCNPNG0030140416');

-- --------------------------------------------------------

--
-- Table structure for table `adx_pesticides`
--

CREATE TABLE `adx_pesticides` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `color_code` varchar(20) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_pesticides`
--

INSERT INTO `adx_pesticides` (`id`, `name`, `icon`, `color_code`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
(1, 'Herbicides', NULL, NULL, 'Chemicals specifically for killing or controlling weeds.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(2, 'Insecticides', NULL, NULL, 'Used to control or eliminate insects.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(3, 'Fungicides', NULL, NULL, 'Target and control fungal diseases.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(4, 'Rodenticides', NULL, NULL, 'Used to control rodents like rats and mice.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(5, 'Bactericides', NULL, NULL, 'Chemicals that control bacterial infections in plants.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(6, 'Nematicides', NULL, NULL, 'Used to control nematodes, which are harmful soil-dwelling worms.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(7, 'Acaricides', NULL, NULL, 'Used to kill mites and ticks.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(8, 'Molluscicides', NULL, NULL, 'Controls mollusks like snails and slugs.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(9, 'Growth Regulators', NULL, NULL, 'Chemicals that regulate the growth and development of plants.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(10, 'Defoliants', NULL, NULL, 'Cause plants to drop leaves, often used in cotton harvesting.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(11, 'Desiccants', NULL, NULL, 'Speed up the drying of plant tissues, used in the harvesting of crops.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(12, 'Repellents', NULL, NULL, 'Used to repel pests rather than kill them.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(13, 'Attractants', NULL, NULL, 'Used to attract pests to a certain area for control.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(14, 'Soil Fumigants', NULL, NULL, 'Chemicals applied to soil to control soil-borne pests and pathogens.', NULL, '2024-11-20 11:38:07', NULL, '2024-11-20 11:38:07'),
(15, 'Bio Pesticides', NULL, '#000000', 'Using bio pesticides and pest controls', 2, '2024-12-02 14:49:35', NULL, '2024-12-02 14:49:35');

-- --------------------------------------------------------

--
-- Table structure for table `adx_province`
--

CREATE TABLE `adx_province` (
  `id` int(11) NOT NULL,
  `provincecode` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `country_id` int(11) NOT NULL,
  `json_id` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_province`
--

INSERT INTO `adx_province` (`id`, `provincecode`, `name`, `country_id`, `json_id`) VALUES
(86, '03', 'Central Province', 1, ''),
(87, '10', 'Chimbu Province', 1, ''),
(88, '11', 'Eastern Highlands Province', 1, ''),
(89, '18', 'East New Britain Province', 1, ''),
(90, '14', 'East Sepik Province', 1, 'OCNPNG003014'),
(91, '08', 'Enga Province', 1, ''),
(92, '02', 'Gulf Province', 1, ''),
(93, '21', 'Hela Province', 1, ''),
(94, '22', 'Jiwaka Province', 1, ''),
(95, '13', 'Madang Province', 1, ''),
(96, '16', 'Manus Province', 1, ''),
(97, '05', 'Milne Bay Province', 1, ''),
(98, '12', 'Morobe Province', 1, ''),
(99, '17', 'New Ireland Province', 1, ''),
(100, '06', 'Oro - Northern Province', 1, ''),
(101, '07', 'Southern Highlands Province', 1, ''),
(102, '01', 'Western Province', 1, ''),
(103, '09', 'Western Highlands Province', 1, ''),
(104, '19', 'West New Britain Province', 1, ''),
(105, '20', 'AROB Bougainville', 1, 'OCNPNG004020');

-- --------------------------------------------------------

--
-- Table structure for table `adx_ward`
--

CREATE TABLE `adx_ward` (
  `id` int(11) NOT NULL,
  `wardcode` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `llg_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_ward`
--

INSERT INTO `adx_ward` (`id`, `wardcode`, `name`, `country_id`, `province_id`, `district_id`, `llg_id`) VALUES
(1, 1, 'Ward 1', 1, 90, 19, 1),
(2, 2, 'Ward 2', 1, 90, 19, 1),
(3, 140502, 'Ward 02', 1, 90, 22, 3),
(4, 140503, 'Ward 03', 1, 90, 22, 3);

-- --------------------------------------------------------

--
-- Table structure for table `climate_focus`
--

CREATE TABLE `climate_focus` (
  `id` int(11) UNSIGNED NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) DEFAULT NULL,
  `gps` varchar(100) NOT NULL,
  `location` varchar(255) NOT NULL,
  `remarks` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `climate_focus`
--

INSERT INTO `climate_focus` (`id`, `country_id`, `province_id`, `district_id`, `gps`, `location`, `remarks`, `status`, `created_at`, `created_by`, `updated_at`, `updated_by`, `deleted_at`, `deleted_by`) VALUES
(1, 1, 90, 19, '-3.569309, 143.600623', 'Sandaun Market', 'See market days', 1, '2025-05-01 17:51:09', 17, '2025-05-01 17:51:09', NULL, NULL, NULL),
(2, 1, 90, 19, '-3.630319, 143.056351', 'Maprik Town', 'Thsi is maprik town', 1, '2025-05-01 18:49:22', 17, '2025-05-01 18:49:22', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_blocks`
--

CREATE TABLE `crops_farm_blocks` (
  `id` int(11) NOT NULL,
  `exercise_id` int(11) DEFAULT NULL,
  `farmer_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `block_code` varchar(50) NOT NULL,
  `org_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `llg_id` int(11) NOT NULL,
  `ward_id` int(11) NOT NULL,
  `village` varchar(100) NOT NULL,
  `block_site` varchar(200) NOT NULL,
  `lon` varchar(50) DEFAULT NULL,
  `lat` varchar(50) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `status` varchar(50) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_blocks`
--

INSERT INTO `crops_farm_blocks` (`id`, `exercise_id`, `farmer_id`, `crop_id`, `block_code`, `org_id`, `country_id`, `province_id`, `district_id`, `llg_id`, `ward_id`, `village`, `block_site`, `lon`, `lat`, `remarks`, `status`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`, `deleted_by`) VALUES
(15, NULL, 4, 1, 'F799463-007', 2, 1, 90, 19, 1, 1, 'Wanara', 'Maunden', '143.590992', '-3.559612', 'Cool man', 'active', 17, '2024-11-09 09:28:32', 17, '2025-01-23 01:01:56', NULL, NULL),
(16, NULL, 2, 4, 'F707995-002', 2, 1, 90, 19, 1, 2, 'Anava', 'Arere lo Wara', '143.362490', '-3.962300', 'hththrth', 'active', 17, '2024-11-09 09:29:53', 17, '2025-01-23 02:35:39', NULL, NULL),
(17, NULL, 4, 5, 'F799463-006', 2, 1, 90, 19, 1, 2, 'Avatip Village', 'Arere lo Wara', '145.611677', '-7.451650', '', 'active', 17, '2024-11-09 09:31:03', 17, '2024-11-30 06:00:25', NULL, NULL),
(18, NULL, 3, 4, 'F712586-008', 2, 1, 90, 19, 1, 1, 'Avatip Village', 'Arere lo Wara', '145.229972', '-5.568096', '', 'active', 17, '2024-11-09 09:34:03', 17, '2024-11-28 06:51:08', NULL, NULL),
(26, NULL, 2, 5, 'F707995-003', 2, 1, 90, 19, 1, 1, 'Villock', 'Nice Peles', '', '', 'thisfs dffk', 'active', 17, '2025-01-10 11:24:03', NULL, '2025-01-10 11:24:03', NULL, NULL),
(27, NULL, 3, 4, 'F712586-009', 2, 1, 90, 19, 1, 2, 'Avatip Village', 'Arere lo Wara', '143.362490', '-3.962300', 'dasdajl', 'active', 17, '2025-01-10 11:25:25', NULL, '2025-01-10 11:25:25', NULL, NULL),
(28, NULL, 5, 5, 'F790802-001', 2, 1, 90, 22, 3, 3, 'Wewak Section', 'Island Stret', '', '', '', 'active', 17, '2025-01-22 06:49:22', NULL, '2025-01-22 06:49:22', NULL, NULL),
(29, NULL, 2, 5, 'F707995-004', 2, 1, 90, 19, 1, 2, 'Tutulo', 'Forex', '', '', '', 'active', 17, '2025-01-23 02:58:31', NULL, '2025-01-23 02:58:31', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_block_files`
--

CREATE TABLE `crops_farm_block_files` (
  `id` int(11) NOT NULL,
  `exercise_id` int(11) DEFAULT NULL,
  `farm_block_id` int(11) NOT NULL,
  `file_caption` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `uploaded_by` int(11) NOT NULL,
  `uploaded_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_crops_data`
--

CREATE TABLE `crops_farm_crops_data` (
  `id` int(11) NOT NULL,
  `exercise_id` int(11) DEFAULT NULL,
  `block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `action_type` enum('add','remove') NOT NULL,
  `action_reason` varchar(100) NOT NULL,
  `number_of_plants` int(11) NOT NULL,
  `breed` varchar(255) DEFAULT NULL,
  `action_date` date NOT NULL,
  `hectares` decimal(10,2) NOT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `status` enum('active','inactive','deleted') NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_crops_data`
--

INSERT INTO `crops_farm_crops_data` (`id`, `exercise_id`, `block_id`, `crop_id`, `action_type`, `action_reason`, `number_of_plants`, `breed`, `action_date`, `hectares`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`, `deleted_at`, `deleted_by`) VALUES
(4, NULL, 11, 47, 'add', 'new planting', 23, 'German', '2024-10-30', 12.00, 'this si the test', 17, '2024-11-09 17:04:05', NULL, '2024-11-09 17:04:05', 'active', NULL, NULL),
(5, NULL, 11, 47, 'add', 'new planting', 23, 'German', '2024-10-30', 12.00, 'this si the test', 17, '2024-11-09 17:06:11', 17, '2024-11-09 17:47:46', 'active', NULL, NULL),
(6, NULL, 11, 47, 'add', 'new planting', 5, 'German', '2024-11-05', 5.00, '', 17, '2024-11-09 17:08:05', 17, '2024-11-09 17:48:20', 'active', NULL, NULL),
(7, NULL, 11, 47, 'remove', 'disease', 23, 'German', '2024-11-06', 5.00, 'This are the disease affected crops', 17, '2024-11-09 17:10:59', NULL, '2024-11-09 17:10:59', 'active', NULL, NULL),
(8, NULL, 11, 47, 'remove', 'disaster', 10, 'German', '2024-10-29', 9.00, 'this is the disaster', 17, '2024-11-09 17:28:05', NULL, '2024-11-09 17:28:05', 'active', NULL, NULL),
(9, NULL, 15, 47, 'add', 'new planting', 10, 'HiBreed', '2024-10-29', 5.00, 'New Hibreeds', 17, '2024-11-10 13:35:49', 17, '2025-01-23 08:34:09', 'active', NULL, NULL),
(10, NULL, 15, 47, 'add', 'new planting', 14, 'German', '2024-11-06', 13.00, 'New Clearance for cocoa', 17, '2024-11-10 13:37:35', 17, '2025-01-23 08:33:56', 'active', NULL, NULL),
(11, NULL, 15, 47, 'remove', 'natural disaster', 3, 'German', '2024-10-26', 0.20, 'a small landslide', 17, '2024-11-10 17:30:24', NULL, '2024-11-10 17:30:24', 'active', NULL, NULL),
(12, NULL, 15, 1, 'remove', 'Disease Infection', 34, 'German', '2024-10-30', 12.00, 'This is the remarks', 17, '2024-11-28 05:30:17', NULL, '2024-11-28 05:30:17', 'active', NULL, NULL),
(13, NULL, 15, 1, 'add', 'new planting', 17, 'German', '2024-10-05', 24.00, 'This is the remarks', 17, '2024-11-28 05:37:47', 17, '2024-11-29 06:59:13', 'active', NULL, NULL),
(14, NULL, 15, 1, 'add', 'Disease Infection', 34, 'German', '2024-11-15', 9.00, 'thisfddf', 17, '2024-11-28 05:44:01', 17, '2025-05-01 07:57:13', 'active', NULL, NULL),
(15, NULL, 15, 1, 'remove', 'Disease Infection', 11, 'Samoan', '2024-11-17', 44.00, 'Remrem', 17, '2024-11-28 06:19:33', NULL, '2024-11-28 06:19:33', 'active', NULL, NULL),
(16, NULL, 15, 1, 'add', 'Prund', 67, 'Pop', '2024-10-29', 6.80, 'afdfk', 17, '2024-11-28 06:21:33', 17, '2024-11-29 06:58:32', 'active', NULL, NULL),
(17, NULL, 18, 4, 'remove', 'Disease Infection', 12, 'Arabika', '2024-07-10', 5.00, 'dafnpkdsnflk', 17, '2024-11-29 07:20:01', NULL, '2024-11-29 07:20:01', 'active', NULL, NULL),
(18, NULL, 18, 4, 'remove', 'Disease Infection', 23, 'Hybrid', '2024-08-10', 16.00, 'Tifsdofhisdhf', 17, '2024-11-29 07:54:31', NULL, '2024-11-29 07:54:31', 'active', NULL, NULL),
(19, NULL, 16, 0, 'add', 'new planting', 12, 'Arabika', '2025-01-05', 5.00, 'This is my new extension for this coffee block', 17, '2025-01-10 10:24:37', 17, '2025-01-10 10:45:35', 'active', NULL, NULL),
(20, NULL, 16, 4, 'add', 'new planting', 9, 'Arabika', '2025-01-07', 10.00, 'this is new extension', 17, '2025-01-10 10:36:48', NULL, '2025-01-10 10:36:48', 'active', NULL, NULL),
(21, NULL, 15, 4, 'add', 'Disease Infection', 110, 'Arabika', '2025-01-07', 10.00, 'hththrth', 17, '2025-01-10 11:44:05', 17, '2025-05-01 07:57:00', 'active', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_disease_data`
--

CREATE TABLE `crops_farm_disease_data` (
  `id` int(11) NOT NULL,
  `exercise_id` int(11) DEFAULT NULL,
  `block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `disease_type` varchar(255) NOT NULL,
  `disease_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `action_reason` text DEFAULT NULL,
  `number_of_plants` int(11) DEFAULT 0,
  `breed` varchar(255) DEFAULT NULL,
  `action_date` datetime DEFAULT NULL,
  `hectares` decimal(10,2) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(50) DEFAULT 'active',
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_disease_data`
--

INSERT INTO `crops_farm_disease_data` (`id`, `exercise_id`, `block_id`, `crop_id`, `disease_type`, `disease_name`, `description`, `action_reason`, `number_of_plants`, `breed`, `action_date`, `hectares`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`, `deleted_at`, `deleted_by`) VALUES
(1, NULL, 15, 1, 'Insect Pests', 'Cocoa Borer', 'This is the disease', 'Disease Infection', 17, 'German', '2024-10-05 00:00:00', 24.00, 'This is the remarks', 17, '2024-11-28 05:30:17', 17, '2024-11-28 06:21:50', 'active', NULL, NULL),
(2, NULL, 15, 1, 'Bacterial Diseases', 'Cocoa Borer', 'thisis ss', 'Disease Infection', 34, 'German', '2024-11-15 00:00:00', 9.00, 'thisfddf', 17, '2024-11-28 05:44:01', 17, '2024-11-28 05:49:05', 'active', NULL, NULL),
(3, NULL, 15, 1, 'Viral Diseases', 'BoraBora', 'This infe', 'Disease Infection', 11, 'Samoan', '2024-11-17 00:00:00', 44.00, 'Remrem', 17, '2024-11-28 06:19:33', NULL, '2024-11-28 06:19:33', 'active', NULL, NULL),
(4, NULL, 15, 1, 'Mites and Other Small Arthropods', 'Binatang', 'tisfhisdhis', 'Disease Infection', 67, 'Pop', '2024-10-29 00:00:00', 6.80, 'afdfk', 17, '2024-11-28 06:21:33', NULL, '2024-11-28 06:21:33', 'active', NULL, NULL),
(5, NULL, 18, 4, 'Fungal Diseases', 'Coffee Fungi', 'Tdsfdfpsdfm', 'Disease Infection', 12, 'Arabika', '2024-07-10 00:00:00', 5.00, 'dafnpkdsnflk', 17, '2024-11-29 07:20:01', NULL, '2024-11-29 07:20:01', 'active', NULL, NULL),
(6, NULL, 18, 4, 'Bird Pests', 'Flower birds', 'TSFSDfojsdfp', 'Disease Infection', 23, 'Hybrid', '2024-08-10 00:00:00', 16.00, 'Tifsdofhisdhf', 17, '2024-11-29 07:54:31', NULL, '2024-11-29 07:54:31', 'active', NULL, NULL),
(7, NULL, 15, 4, 'Viral Diseases', 'Cocoa Borer', 'This is the dry coffee', 'Disease Infection', 110, 'Arabika', '2025-01-07 00:00:00', 10.00, 'hththrth', 17, '2025-01-10 11:44:05', NULL, '2025-01-10 11:44:05', 'active', NULL, NULL),
(8, NULL, 26, 0, 'Nematodes (Parasitic Worms)', 'Worms pulap', 'Thidasdsa mofdsfn', NULL, 34, 'Tyrol', '2025-01-16 00:00:00', 23.00, 'This is a fight for crops', 17, '2025-01-23 09:05:53', 17, '2025-01-23 09:06:14', 'active', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_fertilizer_data`
--

CREATE TABLE `crops_farm_fertilizer_data` (
  `id` int(11) NOT NULL,
  `exercise_id` int(11) DEFAULT NULL,
  `block_id` int(11) NOT NULL,
  `fertilizer_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `brand` varchar(255) DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `unit` decimal(10,2) DEFAULT NULL,
  `quantity` decimal(10,2) DEFAULT NULL,
  `action_date` date DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(50) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_fertilizer_data`
--

INSERT INTO `crops_farm_fertilizer_data` (`id`, `exercise_id`, `block_id`, `fertilizer_id`, `crop_id`, `name`, `brand`, `unit_of_measure`, `unit`, `quantity`, `action_date`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`, `deleted_at`, `deleted_by`) VALUES
(1, NULL, 17, 3, 3, 'Posta', 'Cookf', 'l', NULL, 20.00, '2024-11-12', 'Use this with care', 17, '2024-11-10 14:39:01', 17, '2024-11-10 14:57:53', 'active', NULL, NULL),
(2, NULL, 17, 3, 3, 'Posta', 'Cookf', 'ml', NULL, 20.00, '2024-11-05', 'This is rema', 17, '2024-11-10 14:42:22', NULL, '2024-11-10 14:42:22', 'active', NULL, NULL),
(3, NULL, 15, 1, 1, 'Posta pip', 'Ppop rip', 'l', 23.00, 4.00, '2024-10-30', 'dasdjpasj', 17, '2024-11-20 15:36:47', 17, '2025-01-23 09:10:55', 'active', NULL, NULL),
(4, NULL, 17, 5, 5, 'Posta', 'Gramoxin', 'ml', 32.00, 5.00, '2025-01-08', 'This is gramox', 17, '2025-01-10 11:59:18', 17, '2025-01-10 11:59:47', 'active', NULL, NULL),
(5, NULL, 15, 4, 1, 'Cool Fertix', 'Fertilo', 'l', 23.00, 4.00, '2025-01-16', 'This si the fertix', 17, '2025-01-23 09:10:22', NULL, '2025-01-23 09:10:22', 'active', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_harvest_data`
--

CREATE TABLE `crops_farm_harvest_data` (
  `id` int(11) NOT NULL,
  `exercise_id` int(11) DEFAULT NULL,
  `block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `item` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `unit` decimal(10,2) DEFAULT NULL,
  `quantity` decimal(10,2) DEFAULT NULL,
  `harvest_date` date DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(50) DEFAULT 'active',
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_harvest_data`
--

INSERT INTO `crops_farm_harvest_data` (`id`, `exercise_id`, `block_id`, `crop_id`, `item`, `description`, `unit_of_measure`, `unit`, `quantity`, `harvest_date`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`, `deleted_at`, `deleted_by`) VALUES
(1, NULL, 17, 3, 'Rubber Cups', 'This is the rubber cups', 'cups', 2.00, 30.00, '2024-11-06', 'This si the cup', 17, '2024-11-10 18:16:11', 17, '2024-11-10 18:27:00', 'active', NULL, NULL),
(2, NULL, 15, 1, 'Wet Beans', 'Pure wet beans', 'Buckets', 10.00, 20.00, '2024-10-30', 'This is the new beans', 17, '2024-11-20 15:46:51', 17, '2025-01-23 09:15:21', 'active', NULL, NULL),
(3, NULL, 16, 4, 'Parchment', 'Tfsdifosh', 'Bucket', 12.00, 24.00, '2024-05-23', 'Thisfisdf mfldsfml', 17, '2024-11-29 07:45:02', NULL, '2024-11-29 07:45:02', 'active', NULL, NULL),
(4, NULL, 16, 4, 'Parchment', 'AFDSfsdpj', 'Bilum', 9.00, 33.00, '2024-05-10', 'DFSDfkoj', 17, '2024-11-29 07:46:27', NULL, '2024-11-29 07:46:27', 'active', NULL, NULL),
(5, NULL, 15, 1, 'Froror', 'Dry wan stret', 'Plate', 12.00, 13.00, '2025-01-07', 'Good walo', 17, '2025-01-23 09:16:11', NULL, '2025-01-23 09:16:11', 'active', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_marketing_data`
--

CREATE TABLE `crops_farm_marketing_data` (
  `id` int(11) NOT NULL,
  `exercise_id` int(11) DEFAULT NULL,
  `farmer_id` int(11) NOT NULL,
  `block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `llg_id` int(11) NOT NULL,
  `market_date` date DEFAULT NULL,
  `market_stage` varchar(100) DEFAULT NULL,
  `buyer_id` int(11) DEFAULT NULL,
  `selling_location` varchar(255) DEFAULT NULL,
  `product` varchar(255) NOT NULL,
  `product_type` varchar(200) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `unit` decimal(10,2) DEFAULT NULL,
  `quantity` decimal(10,2) DEFAULT NULL,
  `market_price_per_unit` decimal(10,2) DEFAULT NULL,
  `total_freight_cost` decimal(10,2) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_marketing_data`
--

INSERT INTO `crops_farm_marketing_data` (`id`, `exercise_id`, `farmer_id`, `block_id`, `crop_id`, `country_id`, `province_id`, `district_id`, `llg_id`, `market_date`, `market_stage`, `buyer_id`, `selling_location`, `product`, `product_type`, `description`, `unit_of_measure`, `unit`, `quantity`, `market_price_per_unit`, `total_freight_cost`, `remarks`, `status`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`, `deleted_by`) VALUES
(1, NULL, 4, 0, 5, 0, 0, 0, 0, '2024-11-07', 'primary', 2, 'Maprik', 'Rubber Cups', 'Green Beans', 'Description', 'kg', 2.00, 30.00, 5.50, 200.00, 'REmarkses', 'active', 17, '2024-11-10 20:32:04', 17, '2025-01-02 11:59:43', NULL, NULL),
(2, NULL, 4, 0, 4, 0, 0, 0, 0, '2024-11-06', 'harvest', 4, NULL, 'Coffee Bags', 'Parchment', 'this is the description', 'g', 25.00, 2.00, 200.00, NULL, 'this is remarks', 'active', 17, '2024-11-18 10:38:49', 17, '2025-01-02 11:37:55', NULL, NULL),
(3, NULL, 4, 0, 1, 0, 0, 0, 0, '2024-09-10', 'primary', 11, NULL, 'Dry Beans', 'Dry Beans', 'fjodfjdpajf', 'kg', 63.40, 12.00, 800.00, NULL, 'fsdpfhdsifh', 'active', 17, '2024-11-29 07:32:42', NULL, '2024-11-29 07:32:42', NULL, NULL),
(4, NULL, 4, 17, 5, 0, 90, 22, 3, '2024-10-17', 'primary', 2, 'Wanowo', 'Cup', 'Cuping', 'adfkndsl', 'kg', 1.00, 20.00, 15.00, 2090.00, 'Cooking monkey', 'active', 17, '2024-11-29 07:34:11', NULL, '2025-01-23 13:33:51', NULL, NULL),
(5, NULL, 3, 0, 4, 0, 0, 0, 0, '2023-05-06', 'secondary', 5, '', 'Tadafor', 'Dadsa', 'cafdasdfer', 'l', 3.00, 30.00, 3.40, 400.00, 'asdasm;', 'active', 19, '2025-01-07 12:27:21', 19, '2025-01-07 13:27:11', NULL, NULL),
(6, NULL, 2, 0, 4, 0, 0, 0, 0, '2025-01-09', 'harvest', 2, 'Maprik', 'Dry Coffee', 'Dried', 'This is the dry coffee', 'kg', 50.00, 12.00, 5.00, 200.00, 'This is the wanway coffee', 'active', 17, '2025-01-10 10:50:58', NULL, '2025-01-10 10:50:58', NULL, NULL),
(7, NULL, 4, 15, 1, 0, 90, 22, 3, '2025-01-23', 'primary', 4, '', 'Cocoa Beans', 'Wet Beans', 'Thisfd siaai\'', 'l', 12.00, 34.00, 122.00, NULL, '', 'active', NULL, '2025-01-23 11:04:37', NULL, '2025-01-23 16:52:29', NULL, NULL),
(8, NULL, 4, 15, 4, 0, 90, 22, 3, '2025-01-16', 'primary', 5, 'Gawii', 'Cocoa Beans', 'Wet Beans', '', 'l', 12.00, 34.00, 189.00, 3567.00, 'Cadasdj', 'active', NULL, '2025-01-23 11:08:06', NULL, '2025-01-23 11:08:06', NULL, NULL),
(9, NULL, 4, 17, 5, 0, 90, 19, 2, '2025-01-18', 'harvest', 4, 'Wano', 'Pro this', 'Cherry', 'This cherry', 'l', 2.00, 10.00, 0.40, 600.00, 'This remarks', 'active', NULL, '2025-01-23 16:54:46', NULL, '2025-01-23 16:54:46', NULL, NULL),
(10, NULL, 4, 17, 5, 0, 90, 19, 2, '2025-01-18', 'harvest', 4, 'Wano', 'Pro this', 'Cherry', 'This cherry', 'l', 2.00, 10.00, 0.40, 600.00, 'This remarks', 'active', NULL, '2025-01-23 16:56:26', NULL, '2025-01-23 16:56:26', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_pesticides_data`
--

CREATE TABLE `crops_farm_pesticides_data` (
  `id` int(11) NOT NULL,
  `exercise_id` int(11) DEFAULT NULL,
  `block_id` int(11) NOT NULL,
  `pesticide_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `brand` varchar(255) DEFAULT NULL,
  `unit_of_measure` varchar(50) DEFAULT NULL,
  `unit` decimal(10,2) DEFAULT NULL,
  `quantity` decimal(10,2) DEFAULT NULL,
  `action_date` date DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(50) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crops_farm_pesticides_data`
--

INSERT INTO `crops_farm_pesticides_data` (`id`, `exercise_id`, `block_id`, `pesticide_id`, `crop_id`, `name`, `brand`, `unit_of_measure`, `unit`, `quantity`, `action_date`, `remarks`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`, `deleted_at`, `deleted_by`) VALUES
(1, NULL, 17, 3, 3, 'Run insects', 'Kuku ', 'kg', NULL, 20.00, '2024-10-29', 'This si how much i applied', 17, '2024-11-10 15:13:53', 17, '2024-11-19 04:35:20', 'active', NULL, NULL),
(2, NULL, 15, 6, 1, 'Gramoxeni', 'Gramo', 'l', 20.00, 2.00, '2024-10-30', 'dasdjpasj', 17, '2024-11-20 15:04:33', 17, '2025-01-23 09:13:24', 'active', NULL, NULL),
(3, NULL, 15, 4, 1, 'Ratogen', 'Ratix', 'kg', 12.00, 3.00, '2025-01-17', 'Planti Rat', 17, '2025-01-23 09:13:58', NULL, '2025-01-23 09:13:58', 'active', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `crops_farm_tools`
--

CREATE TABLE `crops_farm_tools` (
  `id` int(11) NOT NULL,
  `tool_type` enum('general','speciality') NOT NULL,
  `crop_id` int(11) DEFAULT NULL,
  `tool_name` varchar(255) NOT NULL,
  `remarks` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` enum('active','inactive') DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `crop_buyers`
--

CREATE TABLE `crop_buyers` (
  `id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `buyer_code` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `address` text DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `operation_span` enum('local','national') NOT NULL,
  `location_id` int(11) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` enum('active','inactive') DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crop_buyers`
--

INSERT INTO `crop_buyers` (`id`, `crop_id`, `buyer_code`, `name`, `address`, `contact_number`, `email`, `operation_span`, `location_id`, `description`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`) VALUES
(1, 1, 'CB1001', 'Green Harvest Ltd.', '123 Green Lane, Springfield', '+123456789', '<EMAIL>', 'local', 90, 'A trusted buyer specializing in organic crops.', 17, '2023-05-10 00:00:00', 17, '2024-11-20 11:13:09', 'active'),
(2, 4, 'CB1002', 'National Produce Co.', '456 Market Road, Riverside', '+234567890', '<EMAIL>', 'national', 1, 'Nationwide distributor of fresh produce.', 17, '2023-06-15 00:00:00', 17, '2025-01-02 14:19:32', 'active'),
(3, 1, 'CB1003', 'Harvest Hub', '789 Rural Ave, Oakville', '+345678901', '<EMAIL>', 'local', 90, 'Small-scale buyer focusing on local markets.', 17, '2023-07-20 00:00:00', 17, '2025-01-02 14:19:42', 'active'),
(4, 5, 'CB1004', 'AgriGlobal Exporters', '101 Export Plaza, Capital City', '+456789012', '<EMAIL>', 'national', 1, 'Leading exporter of agricultural products.', 17, '2023-08-25 00:00:00', 17, '2025-01-02 14:19:53', 'active'),
(5, 5, 'CB1005', 'Farmers Direct', '23 Main Street, Smalltown', '+567890123', '<EMAIL>', 'local', 90, 'Focused on buying directly from small farmers.', 17, '2023-09-30 00:00:00', 17, '2025-01-02 14:20:05', 'active'),
(10, 5, 'CB1006', 'National Produce Co.', 'ffsdfdsfds', '+234567890', '<EMAIL>', 'national', NULL, '', 17, '2024-11-11 10:45:23', 17, '2025-03-05 20:11:37', 'active'),
(11, 45, 'CB1007', 'Eliven', 'Eliven Maprik', '556555', '<EMAIL>', 'local', NULL, 'Raw Tiss  ', 17, '2024-11-11 10:48:34', 17, '2024-11-11 10:49:34', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `crop_processors`
--

CREATE TABLE `crop_processors` (
  `id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `stage` enum('pre-harvest','harvest','post-harvest') NOT NULL,
  `processor_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `crop_processors`
--

INSERT INTO `crop_processors` (`id`, `crop_id`, `stage`, `processor_name`, `description`, `created_at`, `updated_at`) VALUES
(1, 1, 'post-harvest', 'furmentry', 'Dryer', '2023-02-10 00:00:00', '2024-11-11 09:42:51'),
(2, 1, 'harvest', 'pod slicer', 'pod handler', '2023-04-15 00:00:00', '2024-11-11 09:43:03'),
(3, 1, 'pre-harvest', 'cocoa prunner', 'prunist', '2023-06-20 00:00:00', '2024-11-11 09:43:13');

-- --------------------------------------------------------

--
-- Table structure for table `dakoii_org`
--

CREATE TABLE `dakoii_org` (
  `id` int(11) UNSIGNED NOT NULL,
  `orgcode` varchar(500) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `addlockprov` varchar(100) NOT NULL,
  `addlockcountry` varchar(100) NOT NULL,
  `orglogo` varchar(200) NOT NULL,
  `is_locationlocked` tinyint(1) NOT NULL DEFAULT 0,
  `province_json` varchar(255) NOT NULL,
  `district_json` varchar(255) NOT NULL,
  `llg_json` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `license_status` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `dakoii_org`
--

INSERT INTO `dakoii_org` (`id`, `orgcode`, `name`, `description`, `addlockprov`, `addlockcountry`, `orglogo`, `is_locationlocked`, `province_json`, `district_json`, `llg_json`, `is_active`, `license_status`, `created_at`, `updated_at`) VALUES
(2, '2345', 'East Sepik Provincial Administration', 'This is East Sepik Provincial Administration', '90', '1', 'public/uploads/org_logo/2345_1730809835.png', 0, '', '', '', 1, 'paid', '2023-03-16 06:49:23', '2024-11-05 12:30:35'),
(3, '49501', 'Cooking', 'This is cooking descript', '', '', 'http://localhost/promis/public/uploads/org_logo/49501_1679908153.jpg', 0, '', '', '', 0, '', '2023-03-27 09:09:13', '2023-03-27 09:09:13'),
(4, '25492', 'Rico', 'Tekorif', '', '', 'http://localhost/promis/public/uploads/org_logo/25492_1679966568.png', 0, '', '', '', 0, '', '2023-03-27 09:15:40', '2023-03-28 01:22:48'),
(5, '16807', 'Activate', '', '', '', '', 0, '', '', '', 1, '', '2023-03-27 09:19:12', '2023-03-27 09:19:12'),
(6, '53874', 'Oepn Org', 'This Oepn thisdfnfsdj', '', '', 'http://localhost/promis/public/uploads/org_logo/53874_1679914956.jpg', 0, '', '', '', 1, '', '2023-03-27 09:23:18', '2023-03-27 11:02:36'),
(7, '82751', 'Souths PA', 'Southern Highlands Prov Admin', '', '', 'http://localhost/selsys/public/uploads/org_logo/82751_1695260285.png', 0, '', '', '', 1, '', '2023-09-21 01:38:05', '2023-09-21 01:38:05'),
(8, '84261', 'Enga Provincial Administration', '', '', '', 'http://localhost/selsys/public/uploads/org_logo/84261_1719814946.png', 0, '', '', '', 1, '', '2024-07-01 06:22:26', '2024-07-01 06:22:26');

-- --------------------------------------------------------

--
-- Table structure for table `dakoii_users`
--

CREATE TABLE `dakoii_users` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `orgcode` varchar(500) NOT NULL,
  `role` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `dakoii_users`
--

INSERT INTO `dakoii_users` (`id`, `name`, `username`, `password`, `orgcode`, `role`, `is_active`, `created_at`, `updated_at`) VALUES
(2, 'Free Kenny', 'fkenny', '$2y$10$A.8jXDJcv/wbzVi3l8bt/OPY6B0FpExgbUg.HOk6Khq9CYvKNQCyK', '', 'dakoii', 1, '2023-03-16 06:49:23', '2024-07-04 08:27:10');

-- --------------------------------------------------------

--
-- Table structure for table `documents_folder`
--

CREATE TABLE `documents_folder` (
  `id` int(11) UNSIGNED NOT NULL,
  `parent_folder_id` int(11) DEFAULT NULL,
  `folder_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) DEFAULT NULL,
  `llg_id` int(11) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `documents_folder`
--

INSERT INTO `documents_folder` (`id`, `parent_folder_id`, `folder_name`, `description`, `country_id`, `province_id`, `district_id`, `llg_id`, `status`, `created_at`, `created_by`, `updated_at`, `updated_by`, `deleted_at`, `deleted_by`) VALUES
(1, 0, 'Wonderful Folder', 'fshfodh', 1, 1, 19, NULL, 1, '2025-05-01 11:11:35', 17, '2025-05-01 11:11:35', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `document_files`
--

CREATE TABLE `document_files` (
  `id` int(11) UNSIGNED NOT NULL,
  `folder_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_type` varchar(100) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `document_files`
--

INSERT INTO `document_files` (`id`, `folder_id`, `file_name`, `file_path`, `file_type`, `file_size`, `description`, `status`, `created_at`, `created_by`, `updated_at`, `updated_by`, `deleted_at`, `deleted_by`) VALUES
(1, 1, 'This is the name of the file', 'public/uploads/documents/1746062314_88738f47a03d107feb3f.pdf', 'application/pdf', 912290, 'dsfsd;k', 1, '2025-05-01 11:18:34', 17, '2025-05-01 11:18:34', NULL, NULL, NULL),
(2, 1, 'Scanfo', 'public/uploads/documents/1746062340_71e8e52d887ac46ead82.jpg', 'image/jpeg', 265198, '', 1, '2025-05-01 11:19:00', 17, '2025-05-01 11:19:00', NULL, NULL, NULL),
(3, 1, 'EPWAB033.xlsx', 'public/uploads/documents/1746062491_57b6a34378c9c4557c46.xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 6713, '', 1, '2025-05-01 11:21:31', 17, '2025-05-01 11:21:31', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `exercises`
--

CREATE TABLE `exercises` (
  `id` int(11) UNSIGNED NOT NULL,
  `org_id` int(11) UNSIGNED NOT NULL,
  `country_id` int(11) UNSIGNED NOT NULL,
  `province_id` int(11) UNSIGNED NOT NULL,
  `district_id` int(11) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `date_from` date NOT NULL,
  `date_to` date NOT NULL,
  `officer_responsible_id` int(11) UNSIGNED NOT NULL,
  `status` enum('draft','active','submitted','approved','cancelled') NOT NULL DEFAULT 'draft',
  `status_at` datetime DEFAULT NULL,
  `status_by` int(11) UNSIGNED DEFAULT NULL,
  `status_remarks` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) UNSIGNED NOT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `exercises`
--

INSERT INTO `exercises` (`id`, `org_id`, `country_id`, `province_id`, `district_id`, `title`, `description`, `date_from`, `date_to`, `officer_responsible_id`, `status`, `status_at`, `status_by`, `status_remarks`, `created_at`, `created_by`, `updated_at`, `updated_by`, `deleted_at`, `deleted_by`) VALUES
(1, 2, 1, 90, 19, '1st Quarter 2025 Data Collection', 'fsdfhioshd', '2025-04-15', '2025-04-30', 6, 'active', '2025-04-15 19:59:46', 17, 'fsdfsdee', '2025-04-15 18:48:47', 17, '2025-04-15 19:59:46', 17, NULL, NULL),
(2, 2, 1, 90, 19, 'National Agriculture Sector Plan', 'Thifsdi', '2025-04-10', '2025-04-26', 14, 'draft', '2025-04-15 19:36:00', 17, NULL, '2025-04-15 19:36:00', 17, '2025-04-15 19:36:00', NULL, NULL, NULL),
(3, 2, 1, 90, 19, '1st Quarter Farmvisitation ', '', '2025-04-09', '2025-04-09', 8, 'draft', '2025-04-25 14:49:41', 17, NULL, '2025-04-25 14:49:41', 17, '2025-04-25 14:49:41', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `exercise_officers`
--

CREATE TABLE `exercise_officers` (
  `id` int(11) UNSIGNED NOT NULL,
  `exercise_id` int(11) UNSIGNED NOT NULL,
  `user_id` int(11) UNSIGNED NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) UNSIGNED NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `exercise_officers`
--

INSERT INTO `exercise_officers` (`id`, `exercise_id`, `user_id`, `created_at`, `created_by`, `deleted_at`, `deleted_by`) VALUES
(1, 1, 7, '2025-04-15 18:48:47', 17, '2025-04-15 19:18:25', NULL),
(2, 1, 14, '2025-04-15 18:48:47', 17, '2025-04-15 19:18:25', NULL),
(3, 1, 19, '2025-04-15 18:48:47', 17, '2025-04-15 19:18:25', NULL),
(5, 1, 10, '2025-04-15 19:30:10', 1, '2025-04-15 19:30:32', NULL),
(6, 1, 10, '2025-04-15 19:30:10', 1, '2025-04-15 19:30:32', NULL),
(7, 1, 11, '2025-04-15 19:30:10', 1, '2025-04-15 19:30:32', NULL),
(8, 1, 7, '2025-04-15 19:30:32', 17, '2025-04-15 19:30:49', NULL),
(9, 1, 10, '2025-04-15 19:30:32', 17, '2025-04-15 19:30:49', NULL),
(10, 1, 12, '2025-04-15 19:30:32', 17, '2025-04-15 19:30:49', NULL),
(11, 1, 7, '2025-04-15 19:30:49', 17, '2025-04-15 19:31:12', NULL),
(12, 1, 8, '2025-04-15 19:30:49', 17, '2025-04-15 19:31:12', NULL),
(13, 1, 10, '2025-04-15 19:30:49', 17, '2025-04-15 19:31:12', NULL),
(14, 1, 12, '2025-04-15 19:30:49', 17, '2025-04-15 19:31:12', NULL),
(15, 1, 7, '2025-04-15 19:31:12', 17, NULL, NULL),
(16, 1, 8, '2025-04-15 19:31:12', 17, NULL, NULL),
(17, 1, 10, '2025-04-15 19:31:12', 17, NULL, NULL),
(18, 1, 12, '2025-04-15 19:31:12', 17, NULL, NULL),
(19, 2, 8, '2025-04-15 19:36:00', 17, NULL, NULL),
(20, 2, 10, '2025-04-15 19:36:00', 17, NULL, NULL),
(21, 2, 12, '2025-04-15 19:36:00', 17, NULL, NULL),
(22, 3, 7, '2025-04-25 14:49:41', 17, NULL, NULL),
(23, 3, 8, '2025-04-25 14:49:41', 17, NULL, NULL),
(24, 3, 10, '2025-04-25 14:49:41', 17, NULL, NULL),
(25, 3, 11, '2025-04-25 14:49:41', 17, NULL, NULL),
(26, 3, 12, '2025-04-25 14:49:41', 17, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `farmers_children`
--

CREATE TABLE `farmers_children` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `farmer_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `date_of_birth` date NOT NULL,
  `gender` enum('Male','Female') NOT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `farmers_children`
--

INSERT INTO `farmers_children` (`id`, `org_id`, `farmer_id`, `name`, `date_of_birth`, `gender`, `created_by`, `created_at`, `updated_at`, `updated_by`) VALUES
(2, 2, 2, 'Baby Ambunti', '2010-10-29', 'Male', NULL, '2024-11-05 06:32:13', '2024-11-05 07:58:00', NULL),
(3, 2, 2, 'Ambunti Kid 2', '2015-10-29', 'Female', NULL, '2024-11-05 07:58:23', '2024-11-05 07:58:23', NULL),
(5, 2, 3, 'Wan Child', '2007-03-20', 'Male', NULL, '2024-11-27 21:32:21', '2024-11-27 21:32:21', NULL),
(6, 2, 3, 'Kool', '2023-11-07', 'Female', NULL, '2024-11-27 22:04:33', '2024-11-27 22:04:46', 17),
(7, 2, 1, 'Rekrek Pikin', '2003-12-30', 'Female', NULL, '2025-01-02 02:24:59', '2025-03-05 14:03:19', NULL),
(8, 2, 1, 'Wewak Kulablia', '2010-01-04', 'Male', NULL, '2025-01-02 02:25:20', '2025-01-02 02:25:20', NULL),
(9, 2, 1, 'Posta', '2025-03-04', 'Male', NULL, '2025-03-05 14:01:07', '2025-04-14 06:46:38', 17),
(10, 2, 6, 'Wewak Kulablia', '2004-04-02', 'Male', NULL, '2025-04-29 15:41:53', '2025-04-29 15:41:53', NULL),
(11, 2, 6, 'Posta', '2003-04-11', 'Female', NULL, '2025-04-29 15:42:16', '2025-04-29 15:42:16', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `farmer_information`
--

CREATE TABLE `farmer_information` (
  `id` int(11) NOT NULL,
  `org_id` int(11) DEFAULT NULL,
  `farmer_code` varchar(20) NOT NULL,
  `given_name` varchar(50) NOT NULL,
  `surname` varchar(50) NOT NULL,
  `date_of_birth` date NOT NULL,
  `gender` enum('Male','Female') NOT NULL,
  `village` varchar(100) DEFAULT NULL,
  `ward_id` int(11) DEFAULT NULL,
  `llg_id` int(11) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `province_id` int(11) DEFAULT NULL,
  `country_id` int(11) DEFAULT 1,
  `phone` varchar(200) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `marital_status` enum('Single','Married','Divorce','Widow','De-facto') NOT NULL,
  `highest_education_id` int(11) DEFAULT NULL,
  `course_taken` varchar(255) DEFAULT NULL,
  `id_photo` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `farmer_information`
--

INSERT INTO `farmer_information` (`id`, `org_id`, `farmer_code`, `given_name`, `surname`, `date_of_birth`, `gender`, `village`, `ward_id`, `llg_id`, `district_id`, `province_id`, `country_id`, `phone`, `email`, `address`, `marital_status`, `highest_education_id`, `course_taken`, `id_photo`, `created_at`, `created_by`, `updated_at`, `updated_by`, `status`) VALUES
(1, 2, 'F725099', 'Maku', 'Batare', '1990-11-05', 'Male', 'Ambunti Village', 1, 1, 19, 90, 1, '755555', '<EMAIL>', 'Address Lives at Ambunti Station', 'Single', 12, 'This coourse Tken', '', '2024-11-05 05:43:29', 17, '2025-01-22 17:00:50', 17, 'inactive'),
(2, 2, 'F707995', 'Cool Mix', 'Batas', '1982-11-06', 'Male', 'Ambunti Village', 2, 1, 19, 90, 1, '755555', '<EMAIL>', 'This is the address', 'Divorce', 12, 'High School Education', 'public/uploads/farmer_photos/1730806392_3fad0beda40c5c306ef8.jpg', '2024-11-05 05:51:31', 17, '2025-01-22 14:43:41', 17, 'active'),
(3, 2, 'F712586', 'Cool', 'Mangi', '1994-11-05', 'Female', 'Avatip Village', 2, 1, 19, 90, 1, '2534645', '<EMAIL>', 'Along the Sepik River', 'Married', 0, 'Sustainable Farms', 'public/uploads/farmer_photos/1731068924_e2227fbaaeea867de774.jpg', '2024-11-08 12:28:44', 17, '2024-11-27 21:46:20', 17, 'active'),
(4, 2, 'F799463', 'Wanda Gates', 'Gunim', '2004-10-30', 'Male', 'Wandex Village', 1, 1, 19, 90, 1, '7203293', '<EMAIL>', 'This is the addressesss', 'Widow', 10, 'Takin Too Courses', 'public/uploads/farmer_photos/1731069200_4c860a579b3ae29c310e.png', '2024-11-08 12:33:20', 17, '2024-11-28 21:57:05', 17, 'active'),
(5, 2, 'F790802', 'Wewak', 'Mangi', '2002-01-08', 'Male', 'Biam Island', 4, 3, 22, 90, 1, '', '', '', 'Single', 16, '', 'public/uploads/farmer_photos/1737492487_c4d901db19ad2817caa7.jpg', '2025-01-21 20:48:07', 17, '2025-01-21 20:48:07', NULL, 'active'),
(6, 2, 'F790710', 'Aitapes', 'ITUs', '2025-04-02', 'Male', 'Avatip Village', 2, 1, 19, 90, 1, '2534645', '<EMAIL>', 'RRRRR', 'Married', 15, 'BA. Cooking', 'public/uploads/farmer_photos/1745941260_51daf8a23805d319edcc.jpg', '2025-04-29 15:41:00', 17, '2025-04-29 15:41:00', NULL, 'active');

-- --------------------------------------------------------

--
-- Table structure for table `field_visits`
--

CREATE TABLE `field_visits` (
  `id` int(11) UNSIGNED NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `llg_id` int(11) NOT NULL,
  `locations` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`locations`)),
  `gps` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`gps`)),
  `officers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`officers`)),
  `date_start` date NOT NULL,
  `date_end` date NOT NULL,
  `purpose` text NOT NULL,
  `achievements` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`achievements`)),
  `beneficiaries` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`beneficiaries`)),
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `field_visits`
--

INSERT INTO `field_visits` (`id`, `country_id`, `province_id`, `district_id`, `llg_id`, `locations`, `gps`, `officers`, `date_start`, `date_end`, `purpose`, `achievements`, `beneficiaries`, `status`, `created_at`, `created_by`, `updated_at`, `updated_by`, `deleted_at`, `deleted_by`) VALUES
(1, 1, 90, 19, 2, '[\"Gool, Kool, Yokok\"]', '[\"-9.4438, 147.1803\",\"-7.45565,123.4566\",\"5.5455,56.444566\"]', '[{\"id\":\"8\",\"name\":\"Cool Boy\"},{\"id\":\"14\",\"name\":\"Minad\"}]', '2025-05-05', '2025-05-11', 'This is the purpose', '[\"Achive 1\\r\",\"Achive 2\\r\",\"Achie 3\\r\",\"Fkao 4\"]', '[\"Cool man \\r\",\"Ajifid\\r\",\"Farmer \"]', 1, '2025-05-01 06:54:36', 17, '2025-05-01 07:09:03', 17, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `groupings`
--

CREATE TABLE `groupings` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `created_by` varchar(200) DEFAULT NULL,
  `updated_by` varchar(200) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `groupings`
--

INSERT INTO `groupings` (`id`, `org_id`, `name`, `description`, `parent_id`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(2, 2, 'Group one', 'This group one descrtion', NULL, 'minad', NULL, '2024-11-01 05:53:49', '2024-11-01 05:53:49'),
(3, 2, 'Group 1A', 'This is the one A', 2, 'minad', NULL, '2024-11-01 05:56:47', '2024-11-01 05:56:47'),
(4, 2, 'Group 1B', 'This is 1B', 2, 'minad', 'minad', '2024-11-01 05:57:23', '2024-11-01 05:57:37'),
(5, 2, 'Kukul Corporative', 'This is the Kukul Group', NULL, 'Minad', NULL, '2025-01-04 08:53:43', '2025-01-04 08:53:43');

-- --------------------------------------------------------

--
-- Table structure for table `inputs`
--

CREATE TABLE `inputs` (
  `id` int(11) UNSIGNED NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `llg_id` int(11) NOT NULL,
  `locations` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`locations`)),
  `gps` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`gps`)),
  `officers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`officers`)),
  `item` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `unit` varchar(50) NOT NULL,
  `quantity` decimal(10,2) NOT NULL,
  `remarks` text DEFAULT NULL,
  `recipients` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`recipients`)),
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `livestock_farm_blocks`
--

CREATE TABLE `livestock_farm_blocks` (
  `id` int(11) NOT NULL,
  `exercise_id` int(11) DEFAULT NULL,
  `farmer_id` int(11) NOT NULL,
  `block_code` varchar(50) NOT NULL,
  `org_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `llg_id` int(11) NOT NULL,
  `ward_id` int(11) NOT NULL,
  `village` varchar(100) NOT NULL,
  `block_site` varchar(200) NOT NULL,
  `lon` varchar(50) DEFAULT NULL,
  `lat` varchar(50) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `status` varchar(50) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `livestock_farm_blocks`
--

INSERT INTO `livestock_farm_blocks` (`id`, `exercise_id`, `farmer_id`, `block_code`, `org_id`, `country_id`, `province_id`, `district_id`, `llg_id`, `ward_id`, `village`, `block_site`, `lon`, `lat`, `remarks`, `status`, `created_by`, `created_at`, `updated_by`, `updated_at`, `deleted_at`, `deleted_by`) VALUES
(15, NULL, 3, 'LSF712586-010', 2, 1, 90, 19, 1, 2, 'Avatip Village', 'Nice Peles', '143.590992', '-3.559612', 'Cool man this is the cool part', 'inactive', 17, '2024-11-09 09:28:32', 17, '2025-01-03 06:13:31', NULL, NULL),
(16, NULL, 4, 'LSF799463-009', 2, 1, 90, 22, 3, 4, 'Mushu Island', 'Mushu Bay', '143.362490', '-3.962300', 'hththrth', 'active', 17, '2024-11-09 09:29:53', 17, '2025-01-03 08:40:47', NULL, NULL),
(17, NULL, 2, 'LSF707995-001', 2, 1, 90, 19, 1, 2, 'Avatip Village', 'Arere lo Wara', '145.611677', '-7.451650', '', 'active', 17, '2024-11-09 09:31:03', 17, '2025-01-03 06:14:27', NULL, NULL),
(18, NULL, 4, 'LSF799463-008', 2, 1, 90, 19, 1, 1, 'Avatip Village', 'Arere lo Wara', '145.229972', '-5.568096', 'fadsve', 'active', 17, '2024-11-09 09:34:03', 17, '2025-01-03 06:22:31', NULL, NULL),
(19, NULL, 3, 'LSF712586-011', 2, 1, 90, 22, 3, 4, 'One Village', 'One block', '', '', '', 'active', 19, '2025-01-07 13:39:53', NULL, '2025-01-07 13:39:53', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `livestock_farm_data`
--

CREATE TABLE `livestock_farm_data` (
  `id` int(11) NOT NULL,
  `exercise_id` int(11) DEFAULT NULL,
  `block_id` int(11) NOT NULL,
  `livestock_id` int(11) NOT NULL,
  `breed` varchar(255) NOT NULL,
  `he_total` int(11) DEFAULT 0,
  `she_total` int(11) DEFAULT 0,
  `pasture_type` varchar(255) DEFAULT NULL,
  `growth_stage` varchar(255) DEFAULT NULL,
  `cost_per_livestock` varchar(20) NOT NULL,
  `low_price_per_livestock` varchar(20) NOT NULL,
  `high_price_per_livestock` varchar(20) NOT NULL,
  `comments` text DEFAULT NULL,
  `action_date` datetime DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(50) DEFAULT 'active',
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `livestock_farm_data`
--

INSERT INTO `livestock_farm_data` (`id`, `exercise_id`, `block_id`, `livestock_id`, `breed`, `he_total`, `she_total`, `pasture_type`, `growth_stage`, `cost_per_livestock`, `low_price_per_livestock`, `high_price_per_livestock`, `comments`, `action_date`, `created_by`, `created_at`, `updated_by`, `updated_at`, `status`, `deleted_at`, `deleted_by`) VALUES
(1, NULL, 18, 1, 'Braman', 1, 23, 'open pasture', 'calf', '34.50', '50.05', '60.00', 'These are locally breed', '2025-01-01 00:00:00', 17, '2025-01-03 07:33:14', 17, '2025-01-03 08:09:08', 'active', NULL, NULL),
(2, NULL, 18, 2, 'Local ', 4, 2, 'free-range', 'adult', '250', '24', '400', 'local pigs', '2024-12-30 00:00:00', 17, '2025-01-03 07:43:27', 17, '2025-01-03 08:02:45', 'active', NULL, NULL),
(3, NULL, 16, 3, 'High breed', 3, 4, 'barn', 'adult', '202', '34', '50', 'This isi sh ', '2024-12-20 00:00:00', 17, '2025-01-03 08:43:39', NULL, '2025-01-03 08:43:39', 'active', NULL, NULL),
(4, NULL, 17, 2, 'ples', 2, 5, 'open pasture', 'adult', '200', '700', '1200', 'This is hte pigs ples wan', '2025-01-08 00:00:00', 17, '2025-01-10 10:47:43', NULL, '2025-01-10 10:47:43', 'active', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `livestock_production_data`
--

CREATE TABLE `livestock_production_data` (
  `id` int(11) NOT NULL,
  `exercise_id` int(11) DEFAULT NULL,
  `livestock_id` int(11) NOT NULL,
  `item` varchar(255) NOT NULL,
  `unit_of_measure` varchar(50) NOT NULL,
  `unit` decimal(10,2) NOT NULL,
  `price_per_unit` decimal(10,2) DEFAULT NULL,
  `cost_per_unit` decimal(10,2) DEFAULT NULL,
  `quantity` int(11) DEFAULT 0,
  `clients` text DEFAULT NULL,
  `action_date` datetime DEFAULT current_timestamp(),
  `comments` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(50) DEFAULT 'active',
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `permissions_items`
--

CREATE TABLE `permissions_items` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `permission_code` varchar(255) NOT NULL,
  `permission_text` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `permissions_sets`
--

CREATE TABLE `permissions_sets` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `permission_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `permissions_user_districts`
--

CREATE TABLE `permissions_user_districts` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `org_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `default_district` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `permissions_user_districts`
--

INSERT INTO `permissions_user_districts` (`id`, `org_id`, `user_id`, `district_id`, `default_district`, `created_at`, `created_by`, `updated_at`, `updated_by`) VALUES
(1, 2, 19, 22, 0, '2025-01-05 07:53:11', 14, '2025-01-05 08:17:40', 14),
(2, 2, 19, 20, 0, '2025-01-05 07:53:42', 14, '2025-01-05 08:17:40', NULL),
(3, 2, 19, 19, 1, '2025-01-05 08:17:40', 14, '2025-01-05 08:17:40', NULL),
(4, 2, 17, 19, 1, '2025-01-10 00:09:21', 14, '2025-01-10 00:09:21', NULL),
(5, 2, 17, 20, 0, '2025-01-10 01:03:05', 14, '2025-01-10 01:03:05', NULL),
(6, 2, 17, 22, 0, '2025-01-10 01:04:05', 14, '2025-01-10 01:04:05', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `selection`
--

CREATE TABLE `selection` (
  `id` int(11) NOT NULL,
  `box` varchar(20) NOT NULL,
  `value` varchar(200) NOT NULL,
  `item` varchar(200) NOT NULL,
  `hints` text NOT NULL,
  `icons` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `value` varchar(200) NOT NULL,
  `name` varchar(200) NOT NULL,
  `create_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `value`, `name`, `create_at`) VALUES
(1, 'PG', 'country', '2023-03-11 13:50:34');

-- --------------------------------------------------------

--
-- Table structure for table `trainings`
--

CREATE TABLE `trainings` (
  `id` int(11) UNSIGNED NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `llg_id` int(11) NOT NULL,
  `locations` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`locations`)),
  `gps` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`gps`)),
  `date_start` date NOT NULL,
  `date_end` date NOT NULL,
  `topic` varchar(255) NOT NULL,
  `objectives` text NOT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`content`)),
  `trainers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`trainers`)),
  `attendees` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`attendees`)),
  `materials` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`materials`)),
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `trainings`
--

INSERT INTO `trainings` (`id`, `country_id`, `province_id`, `district_id`, `llg_id`, `locations`, `gps`, `date_start`, `date_end`, `topic`, `objectives`, `content`, `trainers`, `attendees`, `materials`, `status`, `created_at`, `created_by`, `updated_at`, `updated_by`, `deleted_at`, `deleted_by`) VALUES
(1, 1, 90, 19, 0, '{\"venue\":\"Vokuku\",\"location\":\"Ples blo wok\"}', '{\"latitude\":\"-9.441348\",\"longitude\":\"147.209842\"}', '2025-05-14', '2025-05-16', 'Child Protection', 'ASDaskp\r\n;dfds;fsd\r\nfsdfjsdkj', '{\"modules\":\"\",\"activities\":\"\",\"notes\":\"\"}', '[]', '[{\"name\":\"Aitapes\",\"id\":\"68131eb558ef6\",\"type\":\"Farmer\",\"farmer_id\":\"223434\",\"gender\":\"Male\",\"age\":\"23\",\"phone\":\"2534645\",\"email\":\"<EMAIL>\",\"added_at\":\"2025-05-01 07:11:55\"},{\"name\":\"Qrew\",\"id\":\"68131eb558f4a\",\"type\":\"NGO Staff\",\"gender\":\"Female\",\"age\":\"25\",\"phone\":\"\",\"email\":\"\",\"added_at\":\"2025-05-01 07:11:55\",\"farmer_id\":\"\"},{\"name\":\"eed\",\"id\":\"68131eb558f7e\",\"type\":\"Farmer\",\"farmer_id\":\"123243\",\"gender\":\"Female\",\"age\":\"40\",\"phone\":\"4234\",\"email\":\"<EMAIL>\",\"added_at\":\"2025-05-01 07:11:55\"}]', '{\"materials\":\"\"}', 3, '2025-05-01 05:33:50', 17, '2025-05-01 07:11:55', 17, NULL, NULL),
(2, 1, 90, 19, 0, '{\"venue\":\"Golok\",\"location\":\"Winim\"}', '{\"latitude\":\"-9.441348\",\"longitude\":\"147.209842\"}', '2025-04-29', '2025-05-10', 'Totlto', 'Objectives likes', '{\"modules\":\"TrINING\",\"activities\":\"aCITVI\",\"notes\":\"Addmition noals\"}', '[]', '[]', '{\"materials\":\"Materials\"}', 1, '2025-05-01 07:13:58', 17, '2025-05-01 07:13:58', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) UNSIGNED NOT NULL,
  `org_id` int(11) NOT NULL,
  `fileno` varchar(100) NOT NULL,
  `name` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','supervisor','user','guest') NOT NULL DEFAULT 'user',
  `position` varchar(255) DEFAULT NULL,
  `id_photo` varchar(500) NOT NULL,
  `phone` varchar(200) NOT NULL,
  `email` varchar(500) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `status_at` datetime DEFAULT NULL,
  `status_by` int(11) DEFAULT NULL,
  `status_remarks` text DEFAULT NULL,
  `created_by` varchar(200) DEFAULT NULL,
  `updated_by` varchar(200) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `org_id`, `fileno`, `name`, `username`, `password`, `role`, `position`, `id_photo`, `phone`, `email`, `status`, `status_at`, `status_by`, `status_remarks`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(6, 2, 'ESP0024', 'Rc4 Admin', 'admin', '$2y$10$u2WThss1GKD/.cWwQl0Gt.q14X83Gz2jxJcFx1Hge5EZxTLpW6ObC', 'admin', 'DASa', '', '', '', 1, NULL, NULL, NULL, NULL, 'minad', '2023-03-17 13:48:51', '2024-11-01 19:23:39'),
(7, 2, 'ESP0025', 'Photo MAngi', 'foto', '$2y$10$lEV0JqkUUrdmkQ.B27ibB.g77Ai1AkEoj3Y2ZU89IAa1ZaUZ4paTu', 'admin', 'PostCol', '', '', '', 0, NULL, NULL, NULL, NULL, 'minad', '2023-03-20 19:21:32', '2024-11-01 19:24:05'),
(8, 2, 'ESP0026', 'Cool Boy', 'cboy', '$2y$10$9834LWBkfvBXjYKWefjFy.zvwZkWtoiLLGKyHr/pxodMusR1GS6fq', 'user', 'Forllo', '', '', '', 1, NULL, NULL, NULL, NULL, 'minad', '2023-03-28 11:10:41', '2024-11-01 19:24:36'),
(10, 2, 'ESP0027', 'Da Moon', 'dada', '$2y$10$XxfirPWI8RkYNrqbHide7.i7MQ7HPVUQkEXqedDoLi8s1yq1JbK.G', 'supervisor', 'Mones', '', '', '', 1, NULL, NULL, NULL, NULL, 'minad', '2023-03-28 11:15:58', '2024-11-01 19:24:53'),
(11, 2, 'ESP0022', 'dafedw', 'dadw', '$2y$10$QtyktCRSvsjPPleLhITOGe1ZOBzDjZ2rNhfTNqTbc5oYbwOlAmsQa', 'user', 'Cokin', '', '', '', 0, NULL, NULL, NULL, NULL, 'minad', '2023-03-28 11:16:44', '2024-11-01 19:25:07'),
(12, 2, 'ESP0020', 'dasda', 'ssdad', '$2y$10$JPDktb1yW3UQ6T8kkSr0BOGyWhhpSjrLef5h4wLIc.gQr8n5FHYUi', 'user', 'Wadji', '', '', '', 0, NULL, NULL, NULL, NULL, 'minad', '2023-03-28 11:19:52', '2024-11-01 19:25:21'),
(13, 2, 'ESP0016', 'DadaFo', 'dadafo', '$2y$10$PQ.vKcORr8iGOzRl32qAPupBtr17LYb61nmJIHS8Hz8nlXWAcfr/.', 'guest', 'Hkolo', '', '', '', 1, NULL, NULL, NULL, NULL, NULL, '2023-03-28 11:28:56', '2025-01-04 18:29:11'),
(14, 2, 'ESP0012', 'Minad', 'minad', '$2y$10$3AUGVe1Sg5jlSCMOb.sG8ewZIzChkyToPrUrMm9QZY1n26lkwmLea', 'admin', 'Flajo', '', '', '', 1, NULL, NULL, NULL, NULL, 'minad', '2023-03-28 11:32:20', '2024-11-01 19:43:04'),
(17, 2, 'ESP0010', 'Wan Tok', 'wbol', '$2y$10$9dJoD/FVRsHmvBv1R/cDquaRL.DH7AJ.6JK6T2bOysOto/4zNuQq6', 'user', 'Rite ', '', '', '<EMAIL>', 1, NULL, NULL, NULL, 'minad', NULL, '2024-11-01 15:24:01', '2025-05-01 09:29:49'),
(19, 2, 'ESP0009', 'Cook Groups', 'cfrend', '$2y$10$luy/ztyNnqEpMTNGy5lya.TXtLTYVQF66WDBQ6mGW5n1F2aKSxNPy', 'user', 'FFOO', '', '', '', 1, NULL, NULL, NULL, 'minad', NULL, '2024-11-01 15:33:12', '2025-01-04 18:26:57');

-- --------------------------------------------------------

--
-- Table structure for table `workplan_infrastructure_activities`
--

CREATE TABLE `workplan_infrastructure_activities` (
  `id` int(11) NOT NULL,
  `workplan_id` int(11) NOT NULL,
  `infrastructure_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `province_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `location` varchar(255) NOT NULL,
  `gps_coordinates` varchar(100) DEFAULT NULL,
  `supervisor_id` int(11) DEFAULT NULL,
  `action_officer_id` int(11) DEFAULT NULL,
  `infrastructure_images` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`infrastructure_images`)),
  `infrastructure_status` enum('pending','submitted','approved','rated') NOT NULL DEFAULT 'pending',
  `infrastructure_status_by` int(11) DEFAULT NULL,
  `infrastructure_status_at` datetime DEFAULT NULL,
  `infrastructure_status_remarks` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `adx_country`
--
ALTER TABLE `adx_country`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_crops`
--
ALTER TABLE `adx_crops`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_district`
--
ALTER TABLE `adx_district`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_education`
--
ALTER TABLE `adx_education`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_fertilizers`
--
ALTER TABLE `adx_fertilizers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_infections`
--
ALTER TABLE `adx_infections`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_livestock`
--
ALTER TABLE `adx_livestock`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_llg`
--
ALTER TABLE `adx_llg`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_pesticides`
--
ALTER TABLE `adx_pesticides`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_province`
--
ALTER TABLE `adx_province`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `adx_ward`
--
ALTER TABLE `adx_ward`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `climate_focus`
--
ALTER TABLE `climate_focus`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_blocks`
--
ALTER TABLE `crops_farm_blocks`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_block_files`
--
ALTER TABLE `crops_farm_block_files`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_crops_data`
--
ALTER TABLE `crops_farm_crops_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_disease_data`
--
ALTER TABLE `crops_farm_disease_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_fertilizer_data`
--
ALTER TABLE `crops_farm_fertilizer_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_harvest_data`
--
ALTER TABLE `crops_farm_harvest_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_marketing_data`
--
ALTER TABLE `crops_farm_marketing_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_pesticides_data`
--
ALTER TABLE `crops_farm_pesticides_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crops_farm_tools`
--
ALTER TABLE `crops_farm_tools`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `crop_buyers`
--
ALTER TABLE `crop_buyers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `buyer_code` (`buyer_code`);

--
-- Indexes for table `crop_processors`
--
ALTER TABLE `crop_processors`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `dakoii_org`
--
ALTER TABLE `dakoii_org`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `dakoii_users`
--
ALTER TABLE `dakoii_users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `documents_folder`
--
ALTER TABLE `documents_folder`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `document_files`
--
ALTER TABLE `document_files`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `exercises`
--
ALTER TABLE `exercises`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exercises_org_id_foreign` (`org_id`),
  ADD KEY `exercises_country_id_foreign` (`country_id`),
  ADD KEY `exercises_province_id_foreign` (`province_id`),
  ADD KEY `exercises_district_id_foreign` (`district_id`),
  ADD KEY `exercises_officer_responsible_id_foreign` (`officer_responsible_id`),
  ADD KEY `exercises_status_index` (`status`);

--
-- Indexes for table `exercise_officers`
--
ALTER TABLE `exercise_officers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exercise_officers_exercise_id_foreign` (`exercise_id`);

--
-- Indexes for table `farmers_children`
--
ALTER TABLE `farmers_children`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `farmer_information`
--
ALTER TABLE `farmer_information`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `field_visits`
--
ALTER TABLE `field_visits`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `groupings`
--
ALTER TABLE `groupings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_parent_id` (`parent_id`);

--
-- Indexes for table `inputs`
--
ALTER TABLE `inputs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `livestock_farm_blocks`
--
ALTER TABLE `livestock_farm_blocks`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `livestock_farm_data`
--
ALTER TABLE `livestock_farm_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `livestock_production_data`
--
ALTER TABLE `livestock_production_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `permissions_items`
--
ALTER TABLE `permissions_items`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `permissions_sets`
--
ALTER TABLE `permissions_sets`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `permissions_user_districts`
--
ALTER TABLE `permissions_user_districts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `selection`
--
ALTER TABLE `selection`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `trainings`
--
ALTER TABLE `trainings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `workplan_infrastructure_activities`
--
ALTER TABLE `workplan_infrastructure_activities`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `adx_country`
--
ALTER TABLE `adx_country`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `adx_crops`
--
ALTER TABLE `adx_crops`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `adx_district`
--
ALTER TABLE `adx_district`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=41;

--
-- AUTO_INCREMENT for table `adx_education`
--
ALTER TABLE `adx_education`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `adx_fertilizers`
--
ALTER TABLE `adx_fertilizers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `adx_infections`
--
ALTER TABLE `adx_infections`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `adx_livestock`
--
ALTER TABLE `adx_livestock`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `adx_llg`
--
ALTER TABLE `adx_llg`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `adx_pesticides`
--
ALTER TABLE `adx_pesticides`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `adx_province`
--
ALTER TABLE `adx_province`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=106;

--
-- AUTO_INCREMENT for table `adx_ward`
--
ALTER TABLE `adx_ward`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `climate_focus`
--
ALTER TABLE `climate_focus`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `crops_farm_blocks`
--
ALTER TABLE `crops_farm_blocks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT for table `crops_farm_block_files`
--
ALTER TABLE `crops_farm_block_files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `crops_farm_crops_data`
--
ALTER TABLE `crops_farm_crops_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `crops_farm_disease_data`
--
ALTER TABLE `crops_farm_disease_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `crops_farm_fertilizer_data`
--
ALTER TABLE `crops_farm_fertilizer_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `crops_farm_harvest_data`
--
ALTER TABLE `crops_farm_harvest_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `crops_farm_marketing_data`
--
ALTER TABLE `crops_farm_marketing_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `crops_farm_pesticides_data`
--
ALTER TABLE `crops_farm_pesticides_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `crops_farm_tools`
--
ALTER TABLE `crops_farm_tools`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `crop_buyers`
--
ALTER TABLE `crop_buyers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `crop_processors`
--
ALTER TABLE `crop_processors`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `dakoii_org`
--
ALTER TABLE `dakoii_org`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `dakoii_users`
--
ALTER TABLE `dakoii_users`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `documents_folder`
--
ALTER TABLE `documents_folder`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `document_files`
--
ALTER TABLE `document_files`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `exercises`
--
ALTER TABLE `exercises`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `exercise_officers`
--
ALTER TABLE `exercise_officers`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `farmers_children`
--
ALTER TABLE `farmers_children`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `farmer_information`
--
ALTER TABLE `farmer_information`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `field_visits`
--
ALTER TABLE `field_visits`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `groupings`
--
ALTER TABLE `groupings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `inputs`
--
ALTER TABLE `inputs`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `livestock_farm_blocks`
--
ALTER TABLE `livestock_farm_blocks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `livestock_farm_data`
--
ALTER TABLE `livestock_farm_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `livestock_production_data`
--
ALTER TABLE `livestock_production_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `permissions_items`
--
ALTER TABLE `permissions_items`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `permissions_sets`
--
ALTER TABLE `permissions_sets`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `permissions_user_districts`
--
ALTER TABLE `permissions_user_districts`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `selection`
--
ALTER TABLE `selection`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=75;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `trainings`
--
ALTER TABLE `trainings`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `workplan_infrastructure_activities`
--
ALTER TABLE `workplan_infrastructure_activities`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `exercise_officers`
--
ALTER TABLE `exercise_officers`
  ADD CONSTRAINT `exercise_officers_exercise_id_foreign` FOREIGN KEY (`exercise_id`) REFERENCES `exercises` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `groupings`
--
ALTER TABLE `groupings`
  ADD CONSTRAINT `fk_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `groupings` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
