<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/data') ?>">Data Management</a></li>
        <li class="breadcrumb-item active">Pesticides</li>
    </ol>
</nav>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Pesticides Management</h2>
        <p class="text-muted mb-0">Manage pesticide types and application methods for pest control</p>
    </div>
    <div class="btn-group">
        <a href="<?= base_url('dakoii/data') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Data
        </a>
        <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#addPesticideModal">
            <i class="fas fa-plus"></i> Add Pesticide
        </button>
    </div>
</div>

<!-- Pesticides Statistics -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Total Pesticides</h6>
                        <h3 class="mb-0"><?= count($pesticides) ?></h3>
                        <small>Pesticide types available in the system</small>
                    </div>
                    <i class="fas fa-spray-can fa-3x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pesticides Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list"></i> Pesticides List
            </h5>
            <input type="text" class="form-control form-control-sm" id="searchInput" 
                   placeholder="Search pesticides..." style="width: 250px;">
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="pesticidesTable">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>Icon</th>
                        <th>Pesticide Name</th>
                        <th>Color Code</th>
                        <th>Remarks</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($pesticides)): ?>
                        <?php $i = 1; foreach ($pesticides as $pesticide): ?>
                        <tr>
                            <td><?= $i++ ?></td>
                            <td>
                                <?php if (!empty($pesticide['icon'])): ?>
                                    <img src="<?= base_url($pesticide['icon']) ?>" alt="Pesticide Icon" 
                                         class="rounded" style="width: 30px; height: 30px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-secondary rounded d-flex align-items-center justify-content-center" 
                                         style="width: 30px; height: 30px;">
                                        <i class="fas fa-spray-can text-white"></i>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="fw-medium"><?= esc($pesticide['name']) ?></div>
                                <small class="text-muted">ID: <?= $pesticide['id'] ?></small>
                            </td>
                            <td>
                                <?php if (!empty($pesticide['color_code'])): ?>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded me-2" 
                                             style="width: 20px; height: 20px; background-color: <?= esc($pesticide['color_code']) ?>; border: 1px solid #dee2e6;"></div>
                                        <code><?= esc($pesticide['color_code']) ?></code>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">No color</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if (!empty($pesticide['remarks'])): ?>
                                    <span title="<?= esc($pesticide['remarks']) ?>">
                                        <?= strlen($pesticide['remarks']) > 50 ? esc(substr($pesticide['remarks'], 0, 50)) . '...' : esc($pesticide['remarks']) ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">No remarks</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary edit-pesticide" 
                                            data-id="<?= $pesticide['id'] ?>"
                                            data-name="<?= esc($pesticide['name']) ?>"
                                            data-icon="<?= esc($pesticide['icon']) ?>"
                                            data-color="<?= esc($pesticide['color_code']) ?>"
                                            data-remarks="<?= esc($pesticide['remarks']) ?>"
                                            data-bs-toggle="modal" 
                                            data-bs-target="#editPesticideModal"
                                            title="Edit Pesticide">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <i class="fas fa-spray-can fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No pesticides found</p>
                                <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#addPesticideModal">
                                    <i class="fas fa-plus"></i> Add First Pesticide
                                </button>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <small class="text-muted">
                Showing <span id="showingCount"><?= count($pesticides) ?></span> of <?= count($pesticides) ?> pesticides
            </small>
        </div>
    </div>
</div>

<!-- Add Pesticide Modal -->
<div class="modal fade" id="addPesticideModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> Add New Pesticide
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open('dakoii/data/pesticides/store') ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="name" class="form-label">Pesticide Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>

                <div class="mb-3">
                    <label for="icon" class="form-label">Icon URL</label>
                    <input type="text" class="form-control" id="icon" name="icon" placeholder="https://example.com/icon.png">
                    <div class="form-text">Enter URL for pesticide icon (optional)</div>
                </div>

                <div class="mb-3">
                    <label for="color_code" class="form-label">Color Code</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="color_code" name="color_code" value="#ffc107">
                        <input type="text" class="form-control" id="color_text" placeholder="#ffc107" readonly>
                    </div>
                    <div class="form-text">Choose a color to represent this pesticide</div>
                </div>

                <div class="mb-3">
                    <label for="remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Additional notes about this pesticide..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-warning">
                    <i class="fas fa-save"></i> Add Pesticide
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Pesticide Modal -->
<div class="modal fade" id="editPesticideModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> Edit Pesticide
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editPesticideForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Pesticide Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_icon" class="form-label">Icon URL</label>
                        <input type="text" class="form-control" id="edit_icon" name="icon" placeholder="https://example.com/icon.png">
                        <div class="form-text">Enter URL for pesticide icon (optional)</div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_color_code" class="form-label">Color Code</label>
                        <div class="input-group">
                            <input type="color" class="form-control form-control-color" id="edit_color_code" name="color_code">
                            <input type="text" class="form-control" id="edit_color_text" readonly>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="edit_remarks" name="remarks" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i> Update Pesticide
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.opacity-75 {
    opacity: 0.75;
}

.table td {
    vertical-align: middle;
}

.form-control-color {
    width: 3rem;
    height: calc(2.25rem + 2px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const table = document.getElementById('pesticidesTable');
    const tbody = table.querySelector('tbody');
    const showingCount = document.getElementById('showingCount');

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = tbody.querySelectorAll('tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        showingCount.textContent = visibleCount;
    });

    // Color picker sync for add modal
    const colorPicker = document.getElementById('color_code');
    const colorText = document.getElementById('color_text');
    
    colorPicker.addEventListener('input', function() {
        colorText.value = this.value;
    });

    // Color picker sync for edit modal
    const editColorPicker = document.getElementById('edit_color_code');
    const editColorText = document.getElementById('edit_color_text');
    
    editColorPicker.addEventListener('input', function() {
        editColorText.value = this.value;
    });

    // Handle edit pesticide button clicks
    document.querySelectorAll('.edit-pesticide').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.dataset.id;
            const name = this.dataset.name;
            const icon = this.dataset.icon;
            const color = this.dataset.color;
            const remarks = this.dataset.remarks;
            
            document.getElementById('edit_name').value = name;
            document.getElementById('edit_icon').value = icon;
            document.getElementById('edit_color_code').value = color || '#ffc107';
            document.getElementById('edit_color_text').value = color || '#ffc107';
            document.getElementById('edit_remarks').value = remarks;
            
            document.getElementById('editPesticideForm').action = '<?= base_url('dakoii/data/pesticides/update/') ?>' + id;
        });
    });

    // Initialize color text on page load
    colorText.value = colorPicker.value;
});
</script>

<?= $this->endSection() ?>
