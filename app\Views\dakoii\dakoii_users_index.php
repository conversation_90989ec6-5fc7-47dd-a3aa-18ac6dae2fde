<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">User Management</h2>
        <p class="text-muted mb-0">Manage system administrators and users</p>
    </div>
    <a href="<?= base_url('dakoii/users/create') ?>" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add User
    </a>
</div>

<!-- User Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Total Users</h6>
                        <h3 class="mb-0"><?= count($users) ?></h3>
                    </div>
                    <i class="fas fa-users fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Active Users</h6>
                        <h3 class="mb-0"><?= count(array_filter($users, fn($u) => $u['is_active'] == 1)) ?></h3>
                    </div>
                    <i class="fas fa-user-check fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Administrators</h6>
                        <h3 class="mb-0"><?= count(array_filter($users, fn($u) => $u['role'] == 'admin')) ?></h3>
                    </div>
                    <i class="fas fa-user-shield fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Moderators</h6>
                        <h3 class="mb-0"><?= count(array_filter($users, fn($u) => $u['role'] == 'moderator')) ?></h3>
                    </div>
                    <i class="fas fa-user-cog fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list"></i> Users List
            </h5>
            <div class="d-flex gap-2">
                <input type="text" class="form-control form-control-sm" id="searchInput" 
                       placeholder="Search users..." style="width: 250px;">
                <select class="form-select form-select-sm" id="roleFilter" style="width: 150px;">
                    <option value="">All Roles</option>
                    <option value="admin">Admin</option>
                    <option value="moderator">Moderator</option>
                    <option value="user">User</option>
                </select>
                <select class="form-select form-select-sm" id="statusFilter" style="width: 150px;">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="usersTable">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>User</th>
                        <th>Username</th>
                        <th>Organization</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $i = 1; foreach ($users as $user): ?>
                    <tr data-role="<?= $user['role'] ?>" 
                        data-status="<?= $user['is_active'] ? 'active' : 'inactive' ?>">
                        <td><?= $i++ ?></td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                     style="width: 40px; height: 40px;">
                                    <span class="text-white fw-bold">
                                        <?= strtoupper(substr($user['name'], 0, 1)) ?>
                                    </span>
                                </div>
                                <div>
                                    <div class="fw-medium"><?= esc($user['name']) ?></div>
                                    <small class="text-muted">ID: <?= $user['id'] ?></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <code class="bg-light px-2 py-1 rounded">@<?= esc($user['username']) ?></code>
                        </td>
                        <td>
                            <?php if (!empty($user['orgcode'])): ?>
                                <?php 
                                $org = array_filter($organizations, fn($o) => $o['orgcode'] == $user['orgcode']);
                                $org = reset($org);
                                ?>
                                <?php if ($org): ?>
                                    <div>
                                        <div class="fw-medium"><?= esc($org['name']) ?></div>
                                        <small class="text-muted"><?= esc($user['orgcode']) ?></small>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted"><?= esc($user['orgcode']) ?></span>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="text-muted">No Organization</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-<?= $user['role'] == 'admin' ? 'danger' : ($user['role'] == 'moderator' ? 'warning' : 'info') ?>">
                                <i class="fas fa-<?= $user['role'] == 'admin' ? 'user-shield' : ($user['role'] == 'moderator' ? 'user-cog' : 'user') ?>"></i>
                                <?= ucfirst($user['role']) ?>
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-<?= $user['is_active'] ? 'success' : 'danger' ?>">
                                <i class="fas fa-<?= $user['is_active'] ? 'check' : 'times' ?>"></i>
                                <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?= base_url('dakoii/users/edit/' . $user['id']) ?>" 
                                   class="btn btn-sm btn-outline-primary" title="Edit User">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php if (session()->get('dakoii_user_id') != $user['id']): ?>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="confirmDelete(<?= $user['id'] ?>, '<?= esc($user['name']) ?>')" 
                                            title="Deactivate User">
                                        <i class="fas fa-user-slash"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <small class="text-muted">
                Showing <span id="showingCount"><?= count($users) ?></span> of <?= count($users) ?> users
            </small>
            <div>
                <small class="text-muted">Total: <?= count($users) ?> users</small>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning"></i> Confirm Deactivation
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to deactivate user <strong id="deleteUserName"></strong>?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle"></i>
                    This will disable the user's access to the system. The user account will not be permanently deleted.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-user-slash"></i> Deactivate User
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.opacity-75 {
    opacity: 0.75;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 0.375rem !important;
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');
    const table = document.getElementById('usersTable');
    const tbody = table.querySelector('tbody');
    const showingCount = document.getElementById('showingCount');

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const roleValue = roleFilter.value;
        const statusValue = statusFilter.value;
        const rows = tbody.querySelectorAll('tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const role = row.dataset.role;
            const status = row.dataset.status;

            const matchesSearch = text.includes(searchTerm);
            const matchesRole = !roleValue || role === roleValue;
            const matchesStatus = !statusValue || status === statusValue;

            if (matchesSearch && matchesRole && matchesStatus) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        showingCount.textContent = visibleCount;
    }

    searchInput.addEventListener('input', filterTable);
    roleFilter.addEventListener('change', filterTable);
    statusFilter.addEventListener('change', filterTable);
});

function confirmDelete(userId, userName) {
    document.getElementById('deleteUserName').textContent = userName;
    document.getElementById('deleteForm').action = '<?= base_url('dakoii/users/delete/') ?>' + userId;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>

<?= $this->endSection() ?>
